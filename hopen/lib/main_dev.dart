// This file serves as an alternative entry point for the application,
// specifically configured to use the `device_preview` package during development.
// It allows developers to easily test the UI on various simulated devices.
// 
// WEB COMPATIBILITY: This version excludes platform-specific dependencies
// that cause JNI compilation errors on web platforms.

import 'package:device_preview/device_preview.dart'; // The device preview package
import 'package:firebase_core/firebase_core.dart'; // Firebase initialization
import 'package:flutter/foundation.dart'; // Provides kReleaseMode, kIsWeb constants
import 'package:flutter/material.dart'; // Core Flutter framework widgets
import 'package:go_router/go_router.dart';

import 'di/injection_container_refactored.dart' as di;
import 'main.dart';

void main() async {
  // --- Standard Application Initialization ---
  // This section largely mirrors the setup in `main.dart` to ensure the app
  // environment is correctly configured before running the UI.

  // Ensure Flutter bindings are initialized before using plugins.
  WidgetsFlutterBinding.ensureInitialized();

  // Initialize Firebase. It uses platform-specific options.
  // For web, it requires explicit configuration (replace placeholders).
  // For mobile, it reads configuration files automatically.
  await Firebase.initializeApp(
    options:
        kIsWeb
            ? FirebaseOptions(
                apiKey: const String.fromEnvironment('FIREBASE_API_KEY', defaultValue: ''),
                authDomain: const String.fromEnvironment('FIREBASE_AUTH_DOMAIN', defaultValue: ''),
                projectId: const String.fromEnvironment('FIREBASE_PROJECT_ID', defaultValue: ''),
                storageBucket: const String.fromEnvironment('FIREBASE_STORAGE_BUCKET', defaultValue: ''),
                messagingSenderId: const String.fromEnvironment('FIREBASE_MESSAGING_SENDER_ID', defaultValue: ''),
                appId: const String.fromEnvironment('FIREBASE_APP_ID', defaultValue: ''),
                measurementId: const String.fromEnvironment('FIREBASE_MEASUREMENT_ID', defaultValue: ''),
              )
            : null, // Use default native initialization
  );

  // Initialize dependency injection container first
  // The DI system will handle platform-specific service initialization
  await di.init();

  // Initialize local notifications service, typically only for mobile platforms.
  // Skip notification service initialization on web to avoid JNI conflicts
  if (!kIsWeb) {
    try {
      // Import notification service dynamically to avoid web compilation issues
      final notificationService = di.sl.get();
      if (notificationService != null && notificationService.runtimeType.toString().contains('NotificationService')) {
        await (notificationService as dynamic).initialize();
      }
    } catch (e) {
      print('Notification service initialization failed: $e');
      // Continue without notifications on web
    }
  }

  // Optional: System UI customization (like status bar style, edge-to-edge).
  // These might be less relevant when using DevicePreview, but are kept here
  // for consistency with the original main.dart if needed.
  // SystemChrome.setSystemUIOverlayStyle(...);
  // SystemChrome.setEnabledSystemUIMode(SystemUiMode.edgeToEdge);

  // Optional: Global error handling setup.
  // FlutterError.onError = (FlutterErrorDetails details) { ... };

  // --- Device Preview Integration ---
  // This is the core difference from `main.dart`. The application's root
  // widget (`MyApp`) is wrapped with the `DevicePreview` widget.

  // Initialize the router from DI, similar to main.dart
  final router = di.sl<GoRouter>();

  runApp(
    DevicePreview(
      // The `builder` provides the actual application widget (`MyApp`)
      // that Device Preview will wrap and display.
      builder: (context) => MyApp(router: router), // Pass the router instance
    ),
  );

  // --- How to Remove Device Preview ---
  // To revert this file to behave like the standard `main.dart` (without Device Preview):
  //
  // 1. Remove the `DevicePreview` widget wrapping:
  //    Replace the `runApp(...)` block above with:
  //    ```dart
  //    runApp(const MyApp());
  //
  //
  // 2. Remove the `device_preview` import:
  //    Delete the line: `import 'package:device_preview/device_preview.dart';`
  //
  // 3. (Optional) Clean up dependencies:
  //    If `device_preview` is no longer needed anywhere in the project,
  //    remove it from `dev_dependencies` in your `pubspec.yaml` file
  //    and run `flutter pub get`.
  //
  // 4. (Optional) Consider deleting this file:
  //    If the only purpose of `main_dev.dart` was to add DevicePreview,
  //    and you remove it, this file might become redundant. You could
  //    then solely rely on `main.dart`.
}
