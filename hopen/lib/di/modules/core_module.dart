import 'dart:io';
import 'package:dio/dio.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/widgets.dart';
import 'package:get_it/get_it.dart';
import 'package:go_router/go_router.dart';
import 'package:http/http.dart' as http;
import 'package:http/io_client.dart';

import '../../config/app_config.dart';
import '../../presentation/router/app_router.dart' as app_router;
import '../../presentation/services/dialog_service_impl.dart';
import '../../provider/config/dio_config.dart';
import '../../provider/services/api/http_api_service.dart';
import '../../provider/services/http3_client_service.dart';


import '../../provider/services/media/media_upload_service.dart';
import '../../provider/services/media/media_picker_service.dart';
import '../../provider/services/storage/storage_service.dart';
import '../../provider/theme/theme_provider.dart';
import '../../provider/utils/retry_mechanism.dart';
import '../../statefulbusinesslogic/core/services/dialog_service.dart';
import '../../statefulbusinesslogic/core/services/security_service.dart';
import '../../statefulbusinesslogic/bloc/notification_permission/notification_permission_bloc.dart';
import '../../provider/services/notification_service_fcm.dart';
import '../../repositories/notification_permission/notification_permission_repository.dart';
import '../../provider/repositories/notification_permission/notification_permission_repository_impl.dart';

/// Core infrastructure module
/// Registers basic services, networking, storage, and utilities
Future<void> registerCoreModule(GetIt sl) async {
  // HTTP & Networking
  sl.registerLazySingleton<http.Client>(() {
    // Create HTTP client with proper SSL handling for development
    if (AppConfig.environment == 'development') {
      final httpClient = HttpClient(context: SecurityContext(withTrustedRoots: false));
      httpClient.badCertificateCallback = (cert, host, port) {
        AppConfig.logInfo('HTTP Client: Development mode - accepting ALL certificates for $host:$port');
        return true;
      };
      return IOClient(httpClient);
    }
    return http.Client();
  });
  sl.registerLazySingleton<Dio>(createDioClient, instanceName: 'apiClient');
  sl.registerLazySingleton<Dio>(
    createFileUploadDioClient,
    instanceName: 'uploadClient',
  );

  // Register HTTP/3 client service as singleton (skip on web to avoid JNI issues)
  if (!kIsWeb) {
    sl.registerLazySingletonAsync<Http3ClientService>(() async {
      final service = Http3ClientService();
      await service.initialize();
      return service;
    });
  }

  sl.registerLazySingleton<HttpApiService>(HttpApiService.new);

  // Storage & Cache
  final storageService = StorageService();
  sl.registerSingleton(storageService);

  // Core Services
  sl.registerLazySingleton<DialogService>(() => DialogServiceImpl());
  sl.registerLazySingleton<RetryMechanism>(RetryMechanism.new);
  sl.registerLazySingleton<ThemeProvider>(ThemeProvider.new);
  sl.registerLazySingleton<SecurityService>(SecurityService.new);

  // Media Upload Service (depends on auth service, will be resolved later)
  sl.registerLazySingleton<MediaUploadService>(
    () => MediaUploadService(authService: sl(), baseUrl: AppConfig.apiBaseUrl),
  );

  // Media Picker Service
  sl.registerLazySingleton<MediaPickerService>(MediaPickerService.new);

  // Configuration
  sl.registerLazySingleton<AppConfig>(AppConfig.new);

  // Notification permission repository
  sl.registerLazySingleton<NotificationPermissionRepository>(
    () => NotificationPermissionRepositoryImpl(
      notificationService: NotificationServiceFCM(),
    ),
  );

  // Notification Permission Bloc
  sl.registerLazySingleton<NotificationPermissionBloc>(
    () => NotificationPermissionBloc(
      repository: sl<NotificationPermissionRepository>(),
    ),
  );

  // Wait for all async dependencies to be ready
  await sl.allReady();

  // Register global navigator key for real-time services
  sl.registerSingleton<GlobalKey<NavigatorState>>(
    GlobalKey<NavigatorState>(),
    instanceName: 'navigatorKey',
  );

  // Router (registered after all dependencies are ready)
  sl.registerSingleton<GoRouter>(app_router.createRouter());
}
