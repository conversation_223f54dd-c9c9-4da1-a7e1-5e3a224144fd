import '../../statefulbusinesslogic/core/models/user_model.dart';
import '../../statefulbusinesslogic/core/error/result.dart';

abstract class UserRepository {
  /// Gets a user by their ID with additional details.
  Future<UserModel?> getUserById(String userId);

  /// Gets a user by their username for profile sharing and deep linking.
  Future<UserModel?> getUserByUsername(String username);

  /// Gets the current user with additional details.
  @Deprecated('Use getCurrentUserSafe for a typed response instead')
  Future<UserModel?> getCurrentUser();

  /// Same as [getCurrentUser] but returns a typed success/failure wrapper.
  Future<Result<UserModel>> getCurrentUserSafe();

  String? get currentUserId;

  Future<UserModel?> getUser(String userId);
  Future<Map<String, dynamic>> getUserInfo(String userId);
  Future<List<UserModel>> getUsers(List<String> userIds);
  Future<List<UserModel>> getAllUsers();
  Future<List<UserModel>> getFriends(String userId);

  Future<void> createUser(UserModel user);
  Future<void> updateUser(UserModel user);

  Future<List<UserModel>> findUsers({
    String? firstName,
    String? lastName,
    String? email,
    // Potentially other filter criteria
  });

  Future<List<UserModel>> getUsersInBubble(String bubbleId);
  Future<Map<String, dynamic>> getGroupInfo(String groupId);

  Future<List<UserModel>> getMutualFriends(String userId1, String userId2);
  Future<List<UserModel>> getMutualContacts(String userId1, String userId2);

  Future<void> addFriend(String userId, String friendId);

  // Contact request methods
  Future<bool> sendContactRequest({required String fromUserId, required String toUserId});
  Future<bool> acceptContactRequest({required String fromUserId, required String toUserId});
  Future<bool> rejectContactRequest({required String fromUserId, required String toUserId});

  // User management methods
  Future<bool> removeContact({required String userId, required String contactId});
  Future<bool> blockUser({required String userId, required String targetUserId});
  Future<bool> unblockUser({required String userId, required String targetUserId});
  Future<bool> reportUser({required String reportedUserId, required String reason, required String category});
}
