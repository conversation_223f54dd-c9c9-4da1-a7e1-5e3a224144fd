import '../exceptions/api_exceptions.dart';
import '../../../statefulbusinesslogic/core/error/exceptions.dart' as core_exceptions;
import '../models/api_models.dart';
import '../services/api/http_api_service.dart';

// ============================================================================
// HTTP Remote Data Source
// ============================================================================

/// Remote data source used throughout provider layer
/// Abstract interface for HTTP remote data operations
abstract class HttpRemoteDataSource {
  // Generic HTTP methods
  Future<dynamic> get(String path);
  Future<dynamic> post(String path, Map<String, dynamic> data);
  Future<dynamic> put(String path, Map<String, dynamic> data);
  Future<dynamic> delete(String path);

  // Authentication - REMOVED: Using OryAuthService exclusively

  // User Profile
  Future<ApiUserProfile> getCurrentUserProfile();
  Future<ApiUserProfile> getUserProfile(String userId);
  Future<ApiUserProfile> getUserProfileByUsername(String username);
  Future<ApiUserProfile> updateUserProfile(UpdateUserProfileRequest request);
  Future<PaginatedUserResponse> getUsers({int? page, int? limit, String? search});
  Future<List<ApiUserProfile>> searchUsers({String query = '', int page = 1, int pageSize = 20});

  // Bubbles
  Future<List<ApiBubble>> getUserBubbles();
  Future<ApiBubble> createBubble(CreateBubbleRequest request);
  Future<ApiBubble> getBubble(String bubbleId);
  Future<void> joinBubble(String bubbleId);
  Future<void> leaveBubble(String bubbleId);
  Future<void> deleteBubble(String bubbleId);
  Future<List<ApiUserProfile>> getBubbleMembers(String bubbleId);

  // Contacts
  Future<List<ApiContact>> getContacts();
  Future<void> sendContactRequest(SendContactRequestRequest request);
  Future<void> acceptContactRequest(String requestId);
  Future<void> rejectContactRequest(String requestId);
  Future<List<ApiUserProfile>> getMutualContacts(String userId);
  // Note: Contact and bubble request models need to be defined
  // Future<List<ApiContactRequest>> getContactRequests();

  // Bubble Requests
  // Future<void> sendBubbleJoinRequest(SendBubbleJoinRequestRequest request);
  // Future<void> acceptBubbleJoinRequest(String requestId);
  // Future<void> rejectBubbleJoinRequest(String requestId);
  // Future<List<ApiBubbleJoinRequest>> getBubbleJoinRequests();

  // Chat
  Future<PaginatedChatMessageResponse> getChatMessages(
    String conversationId, {
    int? page,
    int? limit,
  });
  Future<ApiChatMessage> sendMessage(SendMessageRequest request);
  Future<void> markMessageAsRead(String messageId);
  Future<void> deleteMessage(String messageId);

  // Notifications
  Future<void> registerFCMToken(String token);
  Future<PaginatedNotificationResponse> getNotifications({int? page, int? limit});
  Future<void> markNotificationAsRead(String notificationId);

  // Health
  Future<HealthCheckResponse> healthCheck();

  Future<PaginatedChatMessageResponse> searchChatMessages(
    String conversationId,
    String query, {
    int limit = 50,
  });
}

/// Concrete implementation using HTTP API service
class HttpRemoteDataSourceImpl implements HttpRemoteDataSource {
  HttpRemoteDataSourceImpl({
    required HttpApiService apiService,
  }) : _apiService = apiService;

  final HttpApiService _apiService;

  /// Safe API call wrapper with error handling
  Future<T> safeApiCall<T>(Future<T> Function() apiCall) async {
    try {
      print('🔍 HttpRemoteDataSourceImpl.safeApiCall: Starting API call');
      final result = await apiCall();
      print('🔍 HttpRemoteDataSourceImpl.safeApiCall: API call successful, result type=${result.runtimeType}');
      return result;
    } catch (e, stackTrace) {
      print('🔍 HttpRemoteDataSourceImpl.safeApiCall: API call failed with error=$e');
      print('🔍 HttpRemoteDataSourceImpl.safeApiCall: stackTrace=$stackTrace');
      if (e is ApiException) {
        rethrow;
      }
      throw core_exceptions.NetworkException(message: e.toString());
    }
  }

  // ==========================================================================
  // Generic HTTP Methods
  // ==========================================================================

  @override
  Future<dynamic> get(String path) async {
    return safeApiCall(() => _apiService.get(path));
  }

  @override
  Future<dynamic> post(String path, Map<String, dynamic> data) async {
    return safeApiCall(() => _apiService.post(path, body: data));
  }

  @override
  Future<dynamic> put(String path, Map<String, dynamic> data) async {
    return safeApiCall(() => _apiService.put(path, body: data));
  }

  @override
  Future<dynamic> delete(String path) async {
    return safeApiCall(() => _apiService.delete(path));
  }

  // ==========================================================================
  // Authentication Methods - REMOVED: Using OryAuthService exclusively
  // ==========================================================================
  // All authentication is handled by OryAuthService via Ory Kratos

  // ==========================================================================
  // User Profile Methods
  // ==========================================================================

  @override
  Future<ApiUserProfile> getCurrentUserProfile() async => 
      safeApiCall(() => _apiService.getCurrentUserProfile());

  @override
  Future<ApiUserProfile> getUserProfile(String userId) async =>
      safeApiCall(() => _apiService.getUserProfile(userId));

  @override
  Future<ApiUserProfile> getUserProfileByUsername(String username) async =>
      safeApiCall(() => _apiService.getUserProfileByUsername(username));

  @override
  Future<ApiUserProfile> updateUserProfile(UpdateUserProfileRequest request) async =>
      safeApiCall(() => _apiService.updateUserProfile(request));

  @override
  Future<PaginatedUserResponse> getUsers({int? page, int? limit, String? search}) async {
    String endpoint = '/users';
    if (search != null && search.isNotEmpty) {
      endpoint = '/users/search?q=${Uri.encodeComponent(search)}';
      if (page != null) endpoint += '&page=$page';
      if (limit != null) endpoint += '&page_size=$limit';
    } else {
      final params = <String>[];
      if (page != null) params.add('page=$page');
      if (limit != null) params.add('limit=$limit');
      if (params.isNotEmpty) endpoint += '?${params.join('&')}';

      return safeApiCall(() => _apiService.get<PaginatedUserResponse>(
        endpoint,
        fromJson: PaginatedUserResponse.fromJson,
      ));
    }

    // /users/search – parse SearchUsersResponse schema
    final raw = await safeApiCall(() => _apiService.get<Map<String, dynamic>>(
      endpoint,
    ));

    final List<dynamic> usersJson = raw['users'] as List<dynamic>? ?? [];
    final apiUsers = usersJson.map((e) => ApiUserProfile.fromJson(e as Map<String, dynamic>)).toList();

    return PaginatedUserResponse(
      data: apiUsers,
      page: (raw['page'] ?? 1) as int,
      perPage: (raw['page_size'] ?? apiUsers.length) as int,
      total: (raw['total_count'] ?? apiUsers.length) as int,
      totalPages: 1,
    );
  }

  @override
  Future<List<ApiUserProfile>> searchUsers({String query = '', int page = 1, int pageSize = 20}) async => 
      safeApiCall(() => _apiService.searchUsers(query: query, page: page, pageSize: pageSize));

  // ==========================================================================
  // Bubble Methods
  // ==========================================================================

  @override
  Future<List<ApiBubble>> getUserBubbles() async => 
      safeApiCall(() => _apiService.getUserBubbles());

  @override
  Future<ApiBubble> createBubble(CreateBubbleRequest request) async => 
      safeApiCall(() => _apiService.createBubble(request));

  @override
  Future<ApiBubble> getBubble(String bubbleId) async => 
      safeApiCall(() => _apiService.getBubble(bubbleId));

  @override
  Future<void> joinBubble(String bubbleId) async => 
      safeApiCall(() => _apiService.joinBubble(bubbleId));

  @override
  Future<void> leaveBubble(String bubbleId) async => 
      safeApiCall(() => _apiService.leaveBubble(bubbleId));

  @override
  Future<void> deleteBubble(String bubbleId) async => 
      safeApiCall(() => _apiService.deleteBubble(bubbleId));

  @override
  Future<List<ApiUserProfile>> getBubbleMembers(String bubbleId) async {
    final response = await safeApiCall(() => _apiService.get<List<dynamic>>('/bubbles/$bubbleId/members'));
    return response.map((json) => ApiUserProfile.fromJson(json as Map<String, dynamic>)).toList();
  }

  // ==========================================================================
  // Contact Methods
  // ==========================================================================

  @override
  Future<List<ApiContact>> getContacts() async => 
      safeApiCall(() => _apiService.getContacts());

  @override
  Future<void> sendContactRequest(SendContactRequestRequest request) async => 
      safeApiCall(() => _apiService.sendContactRequest(request));

  @override
  Future<void> acceptContactRequest(String requestId) async {
    return safeApiCall(() => _apiService.post('/contact/requests/$requestId/accept'));
  }

  @override
  Future<void> rejectContactRequest(String requestId) async {
    return safeApiCall(() => _apiService.post('/contact/requests/$requestId/decline'));
  }

  // Note: Contact and bubble request implementations commented out until models are defined
  // @override
  // Future<List<ApiContactRequest>> getContactRequests() async {
  //   final response = await safeApiCall(() => _apiService.get<List<dynamic>>('/contacts/requests'));
  //   return response.map((json) => ApiContactRequest.fromJson(json as Map<String, dynamic>)).toList();
  // }

  // ==========================================================================
  // Bubble Request Methods
  // ==========================================================================

  // @override
  // Future<void> sendBubbleJoinRequest(SendBubbleJoinRequestRequest request) async {
  //   return safeApiCall(() => _apiService.post('/bubbles/requests', body: request.toJson()));
  // }

  // @override
  // Future<void> acceptBubbleJoinRequest(String requestId) async {
  //   return safeApiCall(() => _apiService.post('/bubbles/requests/$requestId/accept'));
  // }

  // @override
  // Future<void> rejectBubbleJoinRequest(String requestId) async {
  //   return safeApiCall(() => _apiService.post('/bubbles/requests/$requestId/reject'));
  // }

  // @override
  // Future<List<ApiBubbleJoinRequest>> getBubbleJoinRequests() async {
  //   final response = await safeApiCall(() => _apiService.get<List<dynamic>>('/bubbles/requests'));
  //   return response.map((json) => ApiBubbleJoinRequest.fromJson(json as Map<String, dynamic>)).toList();
  // }

  // ==========================================================================
  // Chat Methods
  // ==========================================================================

  @override
  Future<PaginatedChatMessageResponse> getChatMessages(
    String conversationId, {
    int? page,
    int? limit,
  }) async {
    return safeApiCall(() => _apiService.get<PaginatedChatMessageResponse>(
      '/chat/$conversationId/messages',
      fromJson: PaginatedChatMessageResponse.fromJson,
    ));
  }

  @override
  Future<ApiChatMessage> sendMessage(SendMessageRequest request) async {
    return safeApiCall(() => _apiService.post<ApiChatMessage>(
      '/chat/messages',
      body: request.toJson(),
      fromJson: ApiChatMessage.fromJson,
    ));
  }

  @override
  Future<void> markMessageAsRead(String messageId) async {
    return safeApiCall(() => _apiService.post('/chat/messages/$messageId/read'));
  }

  @override
  Future<void> deleteMessage(String messageId) async {
    return safeApiCall(() => _apiService.delete('/chat/messages/$messageId'));
  }

  @override
  Future<PaginatedChatMessageResponse> searchChatMessages(
    String conversationId,
    String query, {
    int limit = 50,
  }) async {
    return safeApiCall(() => _apiService.get<PaginatedChatMessageResponse>(
      '/chat/$conversationId/search?q=$query&limit=$limit',
      fromJson: PaginatedChatMessageResponse.fromJson,
    ));
  }

  // ==========================================================================
  // Notification Methods
  // ==========================================================================

  @override
  Future<void> registerFCMToken(String token) async {
    return safeApiCall(() => _apiService.post('/notifications/fcm-token', body: {'token': token}));
  }

  @override
  Future<PaginatedNotificationResponse> getNotifications({int? page, int? limit}) async {
    return safeApiCall(() => _apiService.get<PaginatedNotificationResponse>(
      '/notifications',
      fromJson: PaginatedNotificationResponse.fromJson,
    ));
  }

  @override
  Future<void> markNotificationAsRead(String notificationId) async {
    return safeApiCall(() => _apiService.post('/notifications/$notificationId/read'));
  }

  // ==========================================================================
  // Additional Methods for UserRepository
  // ==========================================================================

  Future<List<ApiUserProfile>> getMutualContacts(String userId) async {
    return safeApiCall(() => _apiService.getMutualContacts(userId));
  }

  // ==========================================================================
  // Health Check
  // ==========================================================================

  @override
  Future<HealthCheckResponse> healthCheck() async =>
      safeApiCall(() => _apiService.healthCheck());
}
