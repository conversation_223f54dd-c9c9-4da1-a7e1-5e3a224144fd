// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'api_models.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

LoginRequest _$LoginRequestFromJson(Map<String, dynamic> json) => LoginRequest(
      email: json['email'] as String,
      password: json['password'] as String,
    );

Map<String, dynamic> _$LoginRequestToJson(LoginRequest instance) =>
    <String, dynamic>{
      'email': instance.email,
      'password': instance.password,
    };

SignupRequest _$SignupRequestFromJson(Map<String, dynamic> json) =>
    SignupRequest(
      email: json['email'] as String,
      password: json['password'] as String,
      firstName: json['first_name'] as String,
      lastName: json['last_name'] as String?,
      username: json['username'] as String?,
      birthday: json['birthday'] == null
          ? null
          : DateTime.parse(json['birthday'] as String),
      profilePictureUrl: json['avatar_url'] as String?,
      notificationsEnabled: json['notifications_enabled'] as bool? ?? false,
    );

Map<String, dynamic> _$SignupRequestToJson(SignupRequest instance) =>
    <String, dynamic>{
      'email': instance.email,
      'password': instance.password,
      'first_name': instance.firstName,
      'last_name': instance.lastName,
      'username': instance.username,
      'birthday': instance.birthday?.toIso8601String(),
      'avatar_url': instance.profilePictureUrl,
      'notifications_enabled': instance.notificationsEnabled,
    };

AuthResponse _$AuthResponseFromJson(Map<String, dynamic> json) => AuthResponse(
      token: json['token'] as String?,
      accessToken: json['access_token'] as String?,
      refreshToken: json['refresh_token'] as String?,
      userId: json['user_id'] as String?,
      expiresIn: (json['expires_in'] as num?)?.toInt(),
      user: json['user'] == null
          ? null
          : ApiUserProfile.fromJson(json['user'] as Map<String, dynamic>),
      message: json['message'] as String?,
      success: json['success'] as bool?,
    );

Map<String, dynamic> _$AuthResponseToJson(AuthResponse instance) =>
    <String, dynamic>{
      'token': instance.token,
      'access_token': instance.accessToken,
      'refresh_token': instance.refreshToken,
      'user_id': instance.userId,
      'expires_in': instance.expiresIn,
      'user': instance.user,
      'message': instance.message,
      'success': instance.success,
    };

RefreshTokenRequest _$RefreshTokenRequestFromJson(Map<String, dynamic> json) =>
    RefreshTokenRequest(
      refreshToken: json['refresh_token'] as String,
    );

Map<String, dynamic> _$RefreshTokenRequestToJson(
        RefreshTokenRequest instance) =>
    <String, dynamic>{
      'refresh_token': instance.refreshToken,
    };

ApiUserProfile _$ApiUserProfileFromJson(Map<String, dynamic> json) =>
    ApiUserProfile(
      id: json['id'] as String,
      email: json['email'] as String,
      firstName: json['first_name'] as String?,
      lastName: json['last_name'] as String?,
      username: json['username'] as String?,
      profilePictureUrl: json['avatar_url'] as String?,
      onlineStatus: json['online_status'] as String?,
      bubbleStatus: json['bubble_status'] as String?,
      birthday: json['birthday'] == null
          ? null
          : DateTime.parse(json['birthday'] as String),
      friendIds: (json['friend_ids'] as List<dynamic>?)
          ?.map((e) => e as String)
          .toList(),
      contactIds: (json['contact_ids'] as List<dynamic>?)
          ?.map((e) => e as String)
          .toList(),
      blockedUserIds: (json['blocked_user_ids'] as List<dynamic>?)
          ?.map((e) => e as String)
          .toList(),
      pendingSentContactRequestIds:
          (json['pending_sent_contact_request_ids'] as List<dynamic>?)
              ?.map((e) => e as String)
              .toList(),
      pendingReceivedContactRequestIds:
          (json['pending_received_contact_request_ids'] as List<dynamic>?)
              ?.map((e) => e as String)
              .toList(),
      pendingSentBubbleRequestUserIds:
          (json['pending_sent_bubble_request_user_ids'] as List<dynamic>?)
              ?.map((e) => e as String)
              .toList(),
      pendingReceivedBubbleRequestUserIds:
          (json['pending_received_bubble_request_user_ids'] as List<dynamic>?)
              ?.map((e) => e as String)
              .toList(),
      bubbleId: json['bubble_id'] as String?,
      isOnline: json['is_online'] as bool?,
      lastSeen: json['last_seen'] == null
          ? null
          : DateTime.parse(json['last_seen'] as String),
      createdAt: json['created_at'] == null
          ? null
          : DateTime.parse(json['created_at'] as String),
      updatedAt: json['updated_at'] == null
          ? null
          : DateTime.parse(json['updated_at'] as String),
      isPrivate: json['is_private'] as bool?,
    );

Map<String, dynamic> _$ApiUserProfileToJson(ApiUserProfile instance) =>
    <String, dynamic>{
      'id': instance.id,
      'email': instance.email,
      'first_name': instance.firstName,
      'last_name': instance.lastName,
      'username': instance.username,
      'avatar_url': instance.profilePictureUrl,
      'online_status': instance.onlineStatus,
      'bubble_status': instance.bubbleStatus,
      'birthday': instance.birthday?.toIso8601String(),
      'friend_ids': instance.friendIds,
      'contact_ids': instance.contactIds,
      'blocked_user_ids': instance.blockedUserIds,
      'pending_sent_contact_request_ids': instance.pendingSentContactRequestIds,
      'pending_received_contact_request_ids':
          instance.pendingReceivedContactRequestIds,
      'pending_sent_bubble_request_user_ids':
          instance.pendingSentBubbleRequestUserIds,
      'pending_received_bubble_request_user_ids':
          instance.pendingReceivedBubbleRequestUserIds,
      'bubble_id': instance.bubbleId,
      'is_online': instance.isOnline,
      'last_seen': instance.lastSeen?.toIso8601String(),
      'created_at': instance.createdAt?.toIso8601String(),
      'updated_at': instance.updatedAt?.toIso8601String(),
      'is_private': instance.isPrivate,
    };

UpdateUserProfileRequest _$UpdateUserProfileRequestFromJson(
        Map<String, dynamic> json) =>
    UpdateUserProfileRequest(
      firstName: json['first_name'] as String?,
      lastName: json['last_name'] as String?,
      username: json['username'] as String?,
      profilePictureUrl: json['avatar_url'] as String?,
    );

Map<String, dynamic> _$UpdateUserProfileRequestToJson(
        UpdateUserProfileRequest instance) =>
    <String, dynamic>{
      'first_name': instance.firstName,
      'last_name': instance.lastName,
      'username': instance.username,
      'avatar_url': instance.profilePictureUrl,
    };

UpdateOnboardingStatusRequest _$UpdateOnboardingStatusRequestFromJson(
        Map<String, dynamic> json) =>
    UpdateOnboardingStatusRequest(
      hasCompletedOnboarding: json['hasCompletedOnboarding'] as bool,
    );

Map<String, dynamic> _$UpdateOnboardingStatusRequestToJson(
        UpdateOnboardingStatusRequest instance) =>
    <String, dynamic>{
      'hasCompletedOnboarding': instance.hasCompletedOnboarding,
    };

ApiBubble _$ApiBubbleFromJson(Map<String, dynamic> json) => ApiBubble(
      id: json['id'] as String,
      name: json['name'] as String,
      maxMembers: (json['max_members'] as num?)?.toInt(),
      currentMembers: (json['current_members'] as num?)?.toInt(),
      currentMemberCount: (json['current_member_count'] as num?)?.toInt(),
      status: json['status'] as String?,
      createdAt: json['created_at'] == null
          ? null
          : DateTime.parse(json['created_at'] as String),
      updatedAt: json['updated_at'] == null
          ? null
          : DateTime.parse(json['updated_at'] as String),
      expiresAt: json['expires_at'] == null
          ? null
          : DateTime.parse(json['expires_at'] as String),
    );

Map<String, dynamic> _$ApiBubbleToJson(ApiBubble instance) => <String, dynamic>{
      'id': instance.id,
      'name': instance.name,
      'max_members': instance.maxMembers,
      'current_members': instance.currentMembers,
      'current_member_count': instance.currentMemberCount,
      'status': instance.status,
      'created_at': instance.createdAt?.toIso8601String(),
      'updated_at': instance.updatedAt?.toIso8601String(),
      'expires_at': instance.expiresAt?.toIso8601String(),
    };

CreateBubbleRequest _$CreateBubbleRequestFromJson(Map<String, dynamic> json) =>
    CreateBubbleRequest(
      name: json['name'] as String,
      maxMembers: (json['max_members'] as num?)?.toInt(),
      invitedUserIds: (json['invited_user_ids'] as List<dynamic>?)
          ?.map((e) => e as String)
          .toList(),
    );

Map<String, dynamic> _$CreateBubbleRequestToJson(
        CreateBubbleRequest instance) =>
    <String, dynamic>{
      'name': instance.name,
      'max_members': instance.maxMembers,
      'invited_user_ids': instance.invitedUserIds,
    };

JoinBubbleRequest _$JoinBubbleRequestFromJson(Map<String, dynamic> json) =>
    JoinBubbleRequest(
      bubbleId: json['bubble_id'] as String,
    );

Map<String, dynamic> _$JoinBubbleRequestToJson(JoinBubbleRequest instance) =>
    <String, dynamic>{
      'bubble_id': instance.bubbleId,
    };

ApiContact _$ApiContactFromJson(Map<String, dynamic> json) => ApiContact(
      id: json['id'] as String,
      firstName: json['first_name'] as String?,
      lastName: json['last_name'] as String?,
      username: json['username'] as String?,
      email: json['email'] as String?,
      profilePictureUrl: json['avatar_url'] as String?,
      relationshipType: json['relationship_type'] as String?,
      isOnline: json['is_online'] as bool?,
      lastSeen: json['last_seen'] == null
          ? null
          : DateTime.parse(json['last_seen'] as String),
      userId: json['user_id'] as String?,
      contactUserId: json['contact_user_id'] as String?,
      senderId: json['senderId'] as String?,
      receiverId: json['receiverId'] as String?,
      senderName: json['senderName'] as String?,
      receiverName: json['receiverName'] as String?,
      sentAt: json['sentAt'] == null
          ? null
          : DateTime.parse(json['sentAt'] as String),
      respondedAt: json['respondedAt'] == null
          ? null
          : DateTime.parse(json['respondedAt'] as String),
      status: json['status'] as String?,
      createdAt: json['created_at'] == null
          ? null
          : DateTime.parse(json['created_at'] as String),
      updatedAt: json['updated_at'] == null
          ? null
          : DateTime.parse(json['updated_at'] as String),
    );

Map<String, dynamic> _$ApiContactToJson(ApiContact instance) =>
    <String, dynamic>{
      'id': instance.id,
      'first_name': instance.firstName,
      'last_name': instance.lastName,
      'username': instance.username,
      'email': instance.email,
      'avatar_url': instance.profilePictureUrl,
      'relationship_type': instance.relationshipType,
      'is_online': instance.isOnline,
      'last_seen': instance.lastSeen?.toIso8601String(),
      'user_id': instance.userId,
      'contact_user_id': instance.contactUserId,
      'senderId': instance.senderId,
      'receiverId': instance.receiverId,
      'senderName': instance.senderName,
      'receiverName': instance.receiverName,
      'sentAt': instance.sentAt?.toIso8601String(),
      'respondedAt': instance.respondedAt?.toIso8601String(),
      'status': instance.status,
      'created_at': instance.createdAt?.toIso8601String(),
      'updated_at': instance.updatedAt?.toIso8601String(),
    };

SendContactRequestRequest _$SendContactRequestRequestFromJson(
        Map<String, dynamic> json) =>
    SendContactRequestRequest(
      recipientId: json['recipient_id'] as String,
      message: json['message'] as String?,
    );

Map<String, dynamic> _$SendContactRequestRequestToJson(
        SendContactRequestRequest instance) =>
    <String, dynamic>{
      'recipient_id': instance.recipientId,
      'message': instance.message,
    };

AcceptContactRequestRequest _$AcceptContactRequestRequestFromJson(
        Map<String, dynamic> json) =>
    AcceptContactRequestRequest(
      contactRequestId: json['contact_request_id'] as String,
    );

Map<String, dynamic> _$AcceptContactRequestRequestToJson(
        AcceptContactRequestRequest instance) =>
    <String, dynamic>{
      'contact_request_id': instance.contactRequestId,
    };

ApiChatMessage _$ApiChatMessageFromJson(Map<String, dynamic> json) =>
    ApiChatMessage(
      id: json['id'] as String,
      senderId: json['sender_id'] as String,
      bubbleId: json['bubble_id'] as String,
      content: json['content'] as String,
      messageType: json['message_type'] as String,
      createdAt: DateTime.parse(json['created_at'] as String),
    );

Map<String, dynamic> _$ApiChatMessageToJson(ApiChatMessage instance) =>
    <String, dynamic>{
      'id': instance.id,
      'sender_id': instance.senderId,
      'bubble_id': instance.bubbleId,
      'content': instance.content,
      'message_type': instance.messageType,
      'created_at': instance.createdAt.toIso8601String(),
    };

SendMessageRequest _$SendMessageRequestFromJson(Map<String, dynamic> json) =>
    SendMessageRequest(
      bubbleId: json['bubble_id'] as String,
      content: json['content'] as String,
      messageType: json['message_type'] as String? ?? 'text',
    );

Map<String, dynamic> _$SendMessageRequestToJson(SendMessageRequest instance) =>
    <String, dynamic>{
      'bubble_id': instance.bubbleId,
      'content': instance.content,
      'message_type': instance.messageType,
    };

GetChatMessagesRequest _$GetChatMessagesRequestFromJson(
        Map<String, dynamic> json) =>
    GetChatMessagesRequest(
      chatId: json['chat_id'] as String,
      limit: (json['limit'] as num?)?.toInt(),
      beforeMessageId: json['before_message_id'] as String?,
    );

Map<String, dynamic> _$GetChatMessagesRequestToJson(
        GetChatMessagesRequest instance) =>
    <String, dynamic>{
      'chat_id': instance.chatId,
      'limit': instance.limit,
      'before_message_id': instance.beforeMessageId,
    };

GetChatMessagesResponse _$GetChatMessagesResponseFromJson(
        Map<String, dynamic> json) =>
    GetChatMessagesResponse(
      messages: (json['messages'] as List<dynamic>)
          .map((e) => ApiChatMessage.fromJson(e as Map<String, dynamic>))
          .toList(),
      hasMore: json['has_more'] as bool,
      nextCursor: json['next_cursor'] as String?,
    );

Map<String, dynamic> _$GetChatMessagesResponseToJson(
        GetChatMessagesResponse instance) =>
    <String, dynamic>{
      'messages': instance.messages,
      'has_more': instance.hasMore,
      'next_cursor': instance.nextCursor,
    };

ApiNotification _$ApiNotificationFromJson(Map<String, dynamic> json) =>
    ApiNotification(
      id: json['id'] as String,
      title: json['title'] as String,
      message: json['message'] as String,
      type: json['type'] as String,
      isRead: json['is_read'] as bool,
      createdAt: DateTime.parse(json['created_at'] as String),
    );

Map<String, dynamic> _$ApiNotificationToJson(ApiNotification instance) =>
    <String, dynamic>{
      'id': instance.id,
      'title': instance.title,
      'message': instance.message,
      'type': instance.type,
      'is_read': instance.isRead,
      'created_at': instance.createdAt.toIso8601String(),
    };

CallInitiateResponse _$CallInitiateResponseFromJson(
        Map<String, dynamic> json) =>
    CallInitiateResponse(
      callId: json['call_id'] as String,
      offerSdp: json['offer_sdp'] as String?,
      iceServers: (json['ice_servers'] as List<dynamic>?)
          ?.map((e) => e as Map<String, dynamic>)
          .toList(),
    );

Map<String, dynamic> _$CallInitiateResponseToJson(
        CallInitiateResponse instance) =>
    <String, dynamic>{
      'call_id': instance.callId,
      'offer_sdp': instance.offerSdp,
      'ice_servers': instance.iceServers,
    };

HealthCheckResponse _$HealthCheckResponseFromJson(Map<String, dynamic> json) =>
    HealthCheckResponse(
      status: json['status'] as String,
      version: json['version'] as String?,
      timestamp: json['timestamp'] == null
          ? null
          : DateTime.parse(json['timestamp'] as String),
      details: json['details'] as Map<String, dynamic>?,
    );

Map<String, dynamic> _$HealthCheckResponseToJson(
        HealthCheckResponse instance) =>
    <String, dynamic>{
      'status': instance.status,
      'version': instance.version,
      'timestamp': instance.timestamp?.toIso8601String(),
      'details': instance.details,
    };

ApiResponseString _$ApiResponseStringFromJson(Map<String, dynamic> json) =>
    ApiResponseString(
      success: json['success'] as bool,
      data: json['data'] as String?,
      message: json['message'] as String?,
      code: (json['code'] as num?)?.toInt(),
    );

Map<String, dynamic> _$ApiResponseStringToJson(ApiResponseString instance) =>
    <String, dynamic>{
      'success': instance.success,
      'data': instance.data,
      'message': instance.message,
      'code': instance.code,
    };

ApiResponseMap _$ApiResponseMapFromJson(Map<String, dynamic> json) =>
    ApiResponseMap(
      success: json['success'] as bool,
      data: json['data'] as Map<String, dynamic>?,
      message: json['message'] as String?,
      code: (json['code'] as num?)?.toInt(),
    );

Map<String, dynamic> _$ApiResponseMapToJson(ApiResponseMap instance) =>
    <String, dynamic>{
      'success': instance.success,
      'data': instance.data,
      'message': instance.message,
      'code': instance.code,
    };

ApiError _$ApiErrorFromJson(Map<String, dynamic> json) => ApiError(
      code: json['code'] as String,
      message: json['message'] as String,
      details: json['details'] as Map<String, dynamic>?,
    );

Map<String, dynamic> _$ApiErrorToJson(ApiError instance) => <String, dynamic>{
      'code': instance.code,
      'message': instance.message,
      'details': instance.details,
    };

PaginatedUserResponse _$PaginatedUserResponseFromJson(
        Map<String, dynamic> json) =>
    PaginatedUserResponse(
      data: (json['data'] as List<dynamic>)
          .map((e) => ApiUserProfile.fromJson(e as Map<String, dynamic>))
          .toList(),
      page: (json['page'] as num).toInt(),
      perPage: (json['per_page'] as num).toInt(),
      total: (json['total'] as num).toInt(),
      totalPages: (json['total_pages'] as num).toInt(),
    );

Map<String, dynamic> _$PaginatedUserResponseToJson(
        PaginatedUserResponse instance) =>
    <String, dynamic>{
      'data': instance.data,
      'page': instance.page,
      'per_page': instance.perPage,
      'total': instance.total,
      'total_pages': instance.totalPages,
    };

PaginatedChatMessageResponse _$PaginatedChatMessageResponseFromJson(
        Map<String, dynamic> json) =>
    PaginatedChatMessageResponse(
      data: (json['data'] as List<dynamic>)
          .map((e) => ApiChatMessage.fromJson(e as Map<String, dynamic>))
          .toList(),
      page: (json['page'] as num).toInt(),
      perPage: (json['per_page'] as num).toInt(),
      total: (json['total'] as num).toInt(),
      totalPages: (json['total_pages'] as num).toInt(),
    );

Map<String, dynamic> _$PaginatedChatMessageResponseToJson(
        PaginatedChatMessageResponse instance) =>
    <String, dynamic>{
      'data': instance.data,
      'page': instance.page,
      'per_page': instance.perPage,
      'total': instance.total,
      'total_pages': instance.totalPages,
    };

PaginatedNotificationResponse _$PaginatedNotificationResponseFromJson(
        Map<String, dynamic> json) =>
    PaginatedNotificationResponse(
      data: (json['data'] as List<dynamic>)
          .map((e) => ApiNotification.fromJson(e as Map<String, dynamic>))
          .toList(),
      page: (json['page'] as num).toInt(),
      perPage: (json['per_page'] as num).toInt(),
      total: (json['total'] as num).toInt(),
      totalPages: (json['total_pages'] as num).toInt(),
    );

Map<String, dynamic> _$PaginatedNotificationResponseToJson(
        PaginatedNotificationResponse instance) =>
    <String, dynamic>{
      'data': instance.data,
      'page': instance.page,
      'per_page': instance.perPage,
      'total': instance.total,
      'total_pages': instance.totalPages,
    };

ApiBlockedUser _$ApiBlockedUserFromJson(Map<String, dynamic> json) =>
    ApiBlockedUser(
      id: json['id'] as String,
      userId: json['user_id'] as String,
      blockedUserId: json['blocked_user_id'] as String,
      blockedAt: DateTime.parse(json['blocked_at'] as String),
      blockedUserName: json['blocked_user_name'] as String?,
      blockedUserUsername: json['blocked_user_username'] as String?,
      reason: json['reason'] as String?,
    );

Map<String, dynamic> _$ApiBlockedUserToJson(ApiBlockedUser instance) =>
    <String, dynamic>{
      'id': instance.id,
      'user_id': instance.userId,
      'blocked_user_id': instance.blockedUserId,
      'blocked_user_name': instance.blockedUserName,
      'blocked_user_username': instance.blockedUserUsername,
      'blocked_at': instance.blockedAt.toIso8601String(),
      'reason': instance.reason,
    };

ApiSupportTicket _$ApiSupportTicketFromJson(Map<String, dynamic> json) =>
    ApiSupportTicket(
      id: json['id'] as String,
      userId: json['user_id'] as String,
      subject: json['subject'] as String,
      description: json['description'] as String,
      status: json['status'] as String,
      priority: json['priority'] as String,
      category: json['category'] as String,
      createdAt: DateTime.parse(json['created_at'] as String),
      updatedAt: json['updated_at'] == null
          ? null
          : DateTime.parse(json['updated_at'] as String),
      resolvedAt: json['resolved_at'] == null
          ? null
          : DateTime.parse(json['resolved_at'] as String),
      assignedTo: json['assigned_to'] as String?,
    );

Map<String, dynamic> _$ApiSupportTicketToJson(ApiSupportTicket instance) =>
    <String, dynamic>{
      'id': instance.id,
      'user_id': instance.userId,
      'subject': instance.subject,
      'description': instance.description,
      'status': instance.status,
      'priority': instance.priority,
      'category': instance.category,
      'created_at': instance.createdAt.toIso8601String(),
      'updated_at': instance.updatedAt?.toIso8601String(),
      'resolved_at': instance.resolvedAt?.toIso8601String(),
      'assigned_to': instance.assignedTo,
    };

BubbleMemberResponse _$BubbleMemberResponseFromJson(
        Map<String, dynamic> json) =>
    BubbleMemberResponse(
      userId: json['user_id'] as String,
      joinedAt: DateTime.parse(json['joined_at'] as String),
      status: json['status'] as String,
      isOnline: json['is_online'] as bool,
      unreadMessageCount: (json['unread_message_count'] as num).toInt(),
      leftAt: json['left_at'] == null
          ? null
          : DateTime.parse(json['left_at'] as String),
      leaveReason: json['leave_reason'] as String?,
    );

Map<String, dynamic> _$BubbleMemberResponseToJson(
        BubbleMemberResponse instance) =>
    <String, dynamic>{
      'user_id': instance.userId,
      'joined_at': instance.joinedAt.toIso8601String(),
      'status': instance.status,
      'is_online': instance.isOnline,
      'left_at': instance.leftAt?.toIso8601String(),
      'leave_reason': instance.leaveReason,
      'unread_message_count': instance.unreadMessageCount,
    };

BubbleResponse _$BubbleResponseFromJson(Map<String, dynamic> json) =>
    BubbleResponse(
      id: json['id'] as String,
      name: json['name'] as String,
      capacity: (json['capacity'] as num).toInt(),
      members: (json['members'] as List<dynamic>)
          .map((e) => BubbleMemberResponse.fromJson(e as Map<String, dynamic>))
          .toList(),
      createdAt: DateTime.parse(json['created_at'] as String),
      status: json['status'] as String,
      endDate: json['end_date'] == null
          ? null
          : DateTime.parse(json['end_date'] as String),
    );

Map<String, dynamic> _$BubbleResponseToJson(BubbleResponse instance) =>
    <String, dynamic>{
      'id': instance.id,
      'name': instance.name,
      'capacity': instance.capacity,
      'members': instance.members,
      'created_at': instance.createdAt.toIso8601String(),
      'end_date': instance.endDate?.toIso8601String(),
      'status': instance.status,
    };

MediaFile _$MediaFileFromJson(Map<String, dynamic> json) => MediaFile(
      id: json['id'] as String,
      userId: json['userId'] as String,
      fileName: json['fileName'] as String,
      originalName: json['originalName'] as String,
      contentType: json['contentType'] as String,
      size: (json['size'] as num).toInt(),
      bucketName: json['bucketName'] as String,
      objectKey: json['objectKey'] as String,
      url: json['url'] as String,
      category: json['category'] as String,
      isPublic: json['isPublic'] as bool,
      createdAt: json['createdAt'] == null
          ? null
          : DateTime.parse(json['createdAt'] as String),
      updatedAt: json['updatedAt'] == null
          ? null
          : DateTime.parse(json['updatedAt'] as String),
    );

Map<String, dynamic> _$MediaFileToJson(MediaFile instance) => <String, dynamic>{
      'id': instance.id,
      'userId': instance.userId,
      'fileName': instance.fileName,
      'originalName': instance.originalName,
      'contentType': instance.contentType,
      'size': instance.size,
      'bucketName': instance.bucketName,
      'objectKey': instance.objectKey,
      'url': instance.url,
      'category': instance.category,
      'isPublic': instance.isPublic,
      'createdAt': instance.createdAt?.toIso8601String(),
      'updatedAt': instance.updatedAt?.toIso8601String(),
    };

UploadParams _$UploadParamsFromJson(Map<String, dynamic> json) => UploadParams(
      category: json['category'] as String,
      isPublic: json['is_public'] as bool? ?? false,
    );

Map<String, dynamic> _$UploadParamsToJson(UploadParams instance) =>
    <String, dynamic>{
      'category': instance.category,
      'is_public': instance.isPublic,
    };

UploadResponse _$UploadResponseFromJson(Map<String, dynamic> json) =>
    UploadResponse(
      success: json['success'] as bool,
      file: MediaFile.fromJson(json['file'] as Map<String, dynamic>),
      message: json['message'] as String,
    );

Map<String, dynamic> _$UploadResponseToJson(UploadResponse instance) =>
    <String, dynamic>{
      'success': instance.success,
      'file': instance.file,
      'message': instance.message,
    };
