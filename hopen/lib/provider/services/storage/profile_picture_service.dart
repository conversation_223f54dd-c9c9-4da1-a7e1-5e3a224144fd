import 'dart:io';

import 'package:flutter/foundation.dart';
import 'package:image/image.dart' as img;
import 'package:image_picker/image_picker.dart';
import 'package:path/path.dart' as path;
import 'package:uuid/uuid.dart';

import '../../config/minio_config.dart';
import 'storage_service.dart';

/// Service for handling profile picture upload, validation, and processing
/// according to the profile picture requirements.
class ProfilePictureService {
  factory ProfilePictureService() => _instance;
  ProfilePictureService._internal();
  static final ProfilePictureService _instance = ProfilePictureService._internal();

  final StorageService _storageService = StorageService();
  final ImagePicker _imagePicker = ImagePicker();
  bool _isStorageInitialized = false;

  // Constants from requirements
  static const int minResolution = 640;
  static const int maxResolution = 1440;
  static const int maxFileSizeBytes = 3 * 1024 * 1024; // 2MB
  static const double compressionQuality = 90;
  static const List<String> allowedFormats = ['jpg', 'jpeg', 'png', 'webp'];

  /// Ensure storage service is properly initialized
  Future<void> _ensureStorageInitialized() async {
    if (_isStorageInitialized) return;
    
    try {
      await _storageService.initialize(
        endpoint: MinioConfig.endpoint,
        accessKey: MinioConfig.accessKey,
        secretKey: MinioConfig.secretKey,
        useSSL: MinioConfig.useSSL,
      );
      _isStorageInitialized = true;
    } catch (e) {
      throw Exception('Failed to initialize storage service: $e');
    }
  }

  /// Pick an image from the gallery with validation
  Future<ProfilePictureResult> pickFromGallery() async {
    try {
      final pickedFile = await _imagePicker.pickImage(
        source: ImageSource.gallery,
        imageQuality: 100, // Keep original quality for validation
      );

      if (pickedFile == null) {
        return ProfilePictureResult.cancelled();
      }

      return await _processImageFile(File(pickedFile.path));
    } catch (e) {
      return ProfilePictureResult.error('Failed to pick image from gallery: $e');
    }
  }

  /// Take a photo with the camera with validation
  Future<ProfilePictureResult> takePhoto() async {
    try {
      final pickedFile = await _imagePicker.pickImage(
        source: ImageSource.camera,
        imageQuality: 100, // Keep original quality for validation
      );

      if (pickedFile == null) {
        return ProfilePictureResult.cancelled();
      }

      return await _processImageFile(File(pickedFile.path));
    } catch (e) {
      return ProfilePictureResult.error('Failed to take photo: $e');
    }
  }

  /// Process and upload a profile picture
  Future<ProfilePictureResult> _processImageFile(File imageFile) async {
    try {
      // Step 1: Ensure storage service is initialized
      await _ensureStorageInitialized();

      // Step 2: Validate file format
      final extension = path.extension(imageFile.path).toLowerCase().replaceAll('.', '');
      if (!allowedFormats.contains(extension)) {
        return ProfilePictureResult.error(
          'Invalid file format. Supported formats: ${allowedFormats.join(', ')}',
        );
      }

      // Step 3: Read and decode image
      final imageBytes = await imageFile.readAsBytes();
      
      final image = img.decodeImage(imageBytes);
      if (image == null) {
        return ProfilePictureResult.error('Invalid image file');
      }

      // Step 4: Validate image specifications (minimum resolution)
      final validationResult = _validateImageSpecs(image);
      if (!validationResult.isValid) {
        return ProfilePictureResult.error(validationResult.error!);
      }

      // Step 5: Process image (resize, crop to square, compress)
      final processedImage = _processImage(image);

      // Step 6: Convert to bytes with compression
      final processedBytes = _encodeImage(processedImage, extension);

      // Step 7: Check processed file size (after compression)
      if (processedBytes.length > maxFileSizeBytes) {
        return ProfilePictureResult.error(
          'Processed image is still too large. Please try a smaller image.',
        );
      }

      // Step 8: Try to upload to storage; if unauthenticated (signup flow) or any failure occurs,
      // fallback to local temporary file path so that the image can be uploaded after account creation.
      String? finalUrl;
      String fileName = '${const Uuid().v4()}.jpg';
      try {
        print('📤 ProfilePictureService: Attempting to upload processed image to storage...');
        finalUrl = await _storageService.uploadData(processedBytes, '.jpg');
        print('✅ ProfilePictureService: Upload successful, URL: $finalUrl');
      } catch (e) {
        print('⚠️ ProfilePictureService: Upload failed (likely unauthenticated during signup): $e');
      }

      if (finalUrl == null) {
        // Store locally and return file path
        print('💾 ProfilePictureService: Storing image locally for later upload after account creation');
        final tempDir = await Directory.systemTemp.createTemp('hopen_pp_');
        final localPath = path.join(tempDir.path, fileName);
        final localFile = File(localPath);
        await localFile.writeAsBytes(processedBytes);
        finalUrl = localFile.path; // Local path to be uploaded later
        print('📁 ProfilePictureService: Stored locally at: $finalUrl');
      }

      return ProfilePictureResult.success(
        url: finalUrl,
        fileName: fileName,
        originalSize: ImageSize(image.width.toDouble(), image.height.toDouble()),
        processedSize: ImageSize(processedImage.width.toDouble(), processedImage.height.toDouble()),
      );
    } catch (e) {
      return ProfilePictureResult.error('Failed to process image: $e');
    }
  }

  /// Validate image specifications according to requirements
  @visibleForTesting
  ImageValidationResult _validateImageSpecs(img.Image image) {
    // Check minimum resolution
    if (image.width < minResolution || image.height < minResolution) {
      return ImageValidationResult.invalid(
        'Image resolution too small. Minimum required: ${minResolution}x$minResolution pixels',
      );
    }

    // Check aspect ratio (must be square or will be made square)
    // We'll accept any aspect ratio and crop to square
    
    return ImageValidationResult.valid();
  }

  /// Process image according to requirements
  @visibleForTesting
  img.Image _processImage(img.Image originalImage) {
    // Step 1: Crop to square (1:1 aspect ratio)
    final squareImage = _cropToSquare(originalImage);

    // Step 2: Resize if larger than max resolution
    var resizedImage = squareImage;
    if (squareImage.width > maxResolution) {
      resizedImage = img.copyResize(
        squareImage,
        width: maxResolution,
        height: maxResolution,
        interpolation: img.Interpolation.cubic,
      );
    }

    // Step 3: If original image is extremely large (> 4000px), do additional reduction
    // This helps with very high resolution camera photos
    if (originalImage.width > 4000 || originalImage.height > 4000) {
      final targetSize = (maxResolution * 0.8).toInt(); // Use 80% of max for very large images
      resizedImage = img.copyResize(
        resizedImage,
        width: targetSize,
        height: targetSize,
        interpolation: img.Interpolation.cubic,
      );
    }

    return resizedImage;
  }

  /// Crop image to square (1:1 aspect ratio)
  @visibleForTesting
  img.Image _cropToSquare(img.Image image) {
    final size = image.width < image.height ? image.width : image.height;
    final x = (image.width - size) ~/ 2;
    final y = (image.height - size) ~/ 2;
    
    return img.copyCrop(image, x: x, y: y, width: size, height: size);
  }

  /// Encode image with compression
  @visibleForTesting
  Uint8List _encodeImage(img.Image image, String originalFormat) {
    // Start with the default quality
    var quality = compressionQuality.toInt();
    Uint8List encodedBytes;
    
    do {
      encodedBytes = Uint8List.fromList(img.encodeJpg(image, quality: quality));
      
      // If the image is still too large, reduce quality
      if (encodedBytes.length > maxFileSizeBytes && quality > 30) {
        quality -= 10;
      } else {
        break;
      }
    } while (quality >= 30);
    
    return encodedBytes;
  }

  /// Show image picker options dialog
  Future<ProfilePictureResult> showPickerDialog() async {
    // This method should be called from UI to show options
    // Implementation will be in the UI layer
    throw UnimplementedError('Use showImagePickerDialog from UI layer');
  }

  /// Generate user initials for fallback avatar
  static String generateInitials(String? firstName, String? lastName) {
    final trimmedFirst = firstName?.trim() ?? '';
    final trimmedLast = lastName?.trim() ?? '';
    
    final first = trimmedFirst.isNotEmpty ? trimmedFirst[0].toUpperCase() : '';
    final last = trimmedLast.isNotEmpty ? trimmedLast[0].toUpperCase() : '';
    
    if (first.isEmpty && last.isEmpty) {
      return 'U'; // Default for User
    }
    
    return '$first$last';
  }

  /// Get avatar color based on user initials
  static int getAvatarColorForInitials(String initials) {
    final colors = [
      0xFF1976D2, // Blue
      0xFF388E3C, // Green
      0xFFF57C00, // Orange
      0xFFD32F2F, // Red
      0xFF7B1FA2, // Purple
      0xFF00796B, // Teal
      0xFFAFB42B, // Lime
      0xFFE64A19, // Deep Orange
    ];
    
    final hash = initials.codeUnits.fold(0, (prev, element) => prev + element);
    return colors[hash % colors.length];
  }
}

/// Result class for profile picture operations
class ProfilePictureResult {

  ProfilePictureResult._({
    required this.isSuccess,
    this.url,
    this.fileName,
    this.error,
    this.originalSize,
    this.processedSize,
    this.isCancelled = false,
  });

  factory ProfilePictureResult.success({
    required String url,
    required String fileName,
    required ImageSize originalSize,
    required ImageSize processedSize,
  }) => ProfilePictureResult._(
      isSuccess: true,
      url: url,
      fileName: fileName,
      originalSize: originalSize,
      processedSize: processedSize,
    );

  factory ProfilePictureResult.error(String error) => ProfilePictureResult._(
      isSuccess: false,
      error: error,
    );

  factory ProfilePictureResult.cancelled() => ProfilePictureResult._(
      isSuccess: false,
      isCancelled: true,
    );
  final bool isSuccess;
  final String? url;
  final String? fileName;
  final String? error;
  final ImageSize? originalSize;
  final ImageSize? processedSize;
  final bool isCancelled;
}

/// Image validation result
class ImageValidationResult {

  ImageValidationResult._(this.isValid, this.error);

  factory ImageValidationResult.valid() => ImageValidationResult._(true, null);
  factory ImageValidationResult.invalid(String error) => ImageValidationResult._(false, error);
  final bool isValid;
  final String? error;
}

/// ImageSize class for image dimensions
class ImageSize {

  const ImageSize(this.width, this.height);
  final double width;
  final double height;

  @override
  String toString() => 'ImageSize(${width.toInt()}x${height.toInt()})';
} 