import 'dart:async';
import 'dart:convert';
import 'dart:io';

import '../../../di/injection_container_refactored.dart' as di;
import '../../../repositories/auth/auth_repository.dart';
import '../../../statefulbusinesslogic/core/error/exceptions.dart';
import '../../../statefulbusinesslogic/core/models/chat_message.dart';
import '../../../statefulbusinesslogic/core/models/send_message_model.dart';
import '../../../statefulbusinesslogic/core/services/logging_service.dart';
import '../../../statefulbusinesslogic/core/models/send_message_model.dart';
import '../../datasources/http_remote_datasource.dart';
import '../../models/api_models.dart';
import '../notification_service_fcm.dart';
import '../mqtt/mqtt_service.dart';
import '../storage/storage_service.dart';

class MessagingService {
  
  MessagingService({
    required HttpRemoteDataSource apiService,
    required MqttService mqttService,
    required StorageService storageService,
    required NotificationServiceFCM fcmService,
  }) : _apiService = apiService,
       _mqttService = mqttService,
       _storageService = storageService,
       _fcmService = fcmService {
    _initializeMessageHandling();
  }
  final HttpRemoteDataSource _apiService;
  final MqttService _mqttService;
  final StorageService _storageService;
  final NotificationServiceFCM _fcmService;
  
  // Message cache for offline support
  final Map<String, List<ChatMessage>> _messageCache = {};
  final List<SendMessageModel> _pendingMessages = [];

  // Stream controllers for real-time updates
  final StreamController<ChatMessage> _newMessageController = StreamController.broadcast();
  final StreamController<String> _messageStatusController = StreamController.broadcast();

  // Streams for real-time message updates
  Stream<ChatMessage> get newMessageStream => _newMessageController.stream;
  Stream<String> get messageStatusStream => _messageStatusController.stream;

  void _initializeMessageHandling() {
    // Listen for incoming messages via MQTT
    _mqttService.messageStream.listen((receivedMessage) {
      // Handle MQTT 5.0 message format
      _handleIncomingMqttMessage(receivedMessage);
    });
  }

  void _handleIncomingMqttMessage(mqttMessage) {
    try {
      final payload = String.fromCharCodes(mqttMessage.payload.message);
      final messageData = jsonDecode(payload) as Map<String, dynamic>;
      
      switch (messageData['type']) {
        case 'new_message':
          _handleNewMessage(messageData);
          break;
        case 'message_status_update':
          _handleMessageStatusUpdate(messageData);
          break;
        case 'typing_indicator':
          _handleTypingIndicator(messageData);
          break;
        default:
          LoggingService.error('Unknown MQTT message type: ${messageData['type']}');
      }
    } catch (e) {
      LoggingService.error('Error handling MQTT message: $e');
    }
  }

  void _handleNewMessage(Map<String, dynamic> messageData) {
    try {
      final message = ChatMessage.fromJson(messageData['message']);

      // Update cache
      final chatId = messageData['chat_id'] as String? ?? message.senderId;
      final cachedMessages = _messageCache[chatId] ?? [];
      cachedMessages.insert(0, message);
      _messageCache[chatId] = cachedMessages;

      // Emit to stream
      _newMessageController.add(message);

      // Send local push notification if app is in background
      _sendLocalNotification(message);
    } catch (e) {
      LoggingService.error('Error handling new message: $e');
    }
  }

  void _handleMessageStatusUpdate(Map<String, dynamic> data) {
    final messageId = data['messageId'];
    final newStatus = data['status'];
    
    // Update cached messages
    for (final messages in _messageCache.values) {
      final messageIndex = messages.indexWhere((msg) => msg.id == messageId);
      if (messageIndex != -1) {
        final updatedMessage = messages[messageIndex].copyWith(
          isRead: newStatus == 'read',
          isDelivered: newStatus == 'delivered' || newStatus == 'read',
        );
        messages[messageIndex] = updatedMessage;
        break;
      }
    }
    
    _messageStatusController.add(messageId);
  }

  void _handleTypingIndicator(Map<String, dynamic> data) {
    // Handle typing indicators
    // This could be implemented with a separate stream if needed
  }

  Future<void> _sendLocalNotification(ChatMessage message) async {
    // Only send notification if message is not from current user
    // This would check current user ID against message sender
    // For now, we'll assume FCM handles this logic
  }

  /// Send a text message
  Future<ChatMessage> sendTextMessage({
    required String chatId,
    required String content,
    String? replyToMessageId,
    bool isGroupChat = false,
  }) async {
    try {
      final messageModel = SendMessageModel(
        chatId: chatId,
        content: content,
        messageType: MediaType.text,
        replyToMessageId: replyToMessageId,
      );

      return await _sendMessage(messageModel, isGroupChat);
    } catch (e) {
      throw MessagingException('Failed to send text message: $e');
    }
  }

  /// Send a message with media attachment
  Future<ChatMessage> sendMediaMessage({
    required String chatId,
    required File mediaFile,
    required MediaType messageType,
    String? caption,
    String? replyToMessageId,
    bool isGroupChat = false,
  }) async {
    try {
      // Upload media file first
      final mediaUrl = await _uploadMedia(mediaFile, messageType);

      final messageModel = SendMessageModel(
        chatId: chatId,
        content: caption ?? '',
        messageType: messageType,
        mediaUrl: mediaUrl,
        replyToMessageId: replyToMessageId,
      );

      return await _sendMessage(messageModel, isGroupChat);
    } catch (e) {
      throw MessagingException('Failed to send media message: $e');
    }
  }

  /// Upload media file to storage
  Future<String?> _uploadMedia(File mediaFile, MediaType messageType) async {
    try {
      String folder;
      switch (messageType) {
        case MediaType.image:
          folder = 'messages/images';
          break;
        case MediaType.video:
          folder = 'messages/videos';
          break;
        case MediaType.audio:
          folder = 'messages/audio';
          break;
        case MediaType.file:
          folder = 'messages/files';
          break;
        default:
          folder = 'messages/files';
      }

      final fileName = '${DateTime.now().millisecondsSinceEpoch}_${mediaFile.path.split('/').last}';
      final uploadedUrl = await _storageService.uploadFile(mediaFile, '$folder/$fileName');
      return uploadedUrl;
    } catch (e) {
      throw MessagingException('Failed to upload media: $e');
    }
  }

  /// Core message sending logic
  Future<ChatMessage> _sendMessage(SendMessageModel messageModel, bool isGroupChat) async {
    try {
      // Get current user from auth service
      final authRepository = di.sl<AuthRepository>();
      final currentUserResult = await authRepository.getCurrentUser();
      final currentUserId = currentUserResult.isSuccess && currentUserResult.data != null
          ? currentUserResult.data!.id
          : 'anonymous_user';
      final currentUserName = currentUserResult.isSuccess && currentUserResult.data != null
          ? '${currentUserResult.data!.firstName} ${currentUserResult.data!.lastName}'
          : 'You';

      // Create optimistic message for immediate UI update
      final optimisticMessage = ChatMessage(
        id: 'temp_${DateTime.now().millisecondsSinceEpoch}',
        content: messageModel.content,
        senderId: currentUserId,
        timestamp: DateTime.now(),
        mediaType: messageModel.messageType,
        mediaUrl: messageModel.mediaUrl,
        isMe: true,
        isRead: false,
        isDelivered: false,
      );

      // Add to cache for immediate display
      final cachedMessages = _messageCache[messageModel.chatId] ?? [];
      cachedMessages.insert(0, optimisticMessage);
      _messageCache[messageModel.chatId] = cachedMessages;
      
      // Emit optimistic message
      _newMessageController.add(optimisticMessage);

      // Send via API
      final request = SendMessageRequest(
        bubbleId: messageModel.chatId,
        content: messageModel.content,
        messageType: messageModel.messageType.name,
      );
      final sentMessage = await _apiService.sendMessage(request);

      // Convert API response to ChatMessage
      final messageResult = ChatMessage(
        id: sentMessage.id,
        content: sentMessage.content,
        senderId: sentMessage.senderId,
        timestamp: sentMessage.createdAt,
        mediaType: _parseMediaType(sentMessage.messageType),
        isMe: true,
        isRead: false,
        isDelivered: true,
      );

      // Update cache with real message
      final messageIndex = cachedMessages.indexWhere((msg) => msg.id == optimisticMessage.id);
      if (messageIndex != -1) {
        cachedMessages[messageIndex] = messageResult;
      }

      // Send real-time notification via MQTT
      await _sendMqttNotification(messageResult, isGroupChat);

      // Send push notification to recipients
      await _sendPushNotification(messageResult, isGroupChat);

      return messageResult;
    } catch (e) {
      // Update optimistic message to show error
      final cachedMessages = _messageCache[messageModel.chatId] ?? [];
      final messageIndex = cachedMessages.indexWhere((msg) => msg.id.startsWith('temp_'));
      if (messageIndex != -1) {
        final errorMessage = cachedMessages[messageIndex].copyWith(
          // ChatMessage doesn't have status, we'll just keep it as is
        );
        cachedMessages[messageIndex] = errorMessage;
      }
      
      rethrow;
    }
  }

  /// Send MQTT notification for real-time delivery
  Future<void> _sendMqttNotification(ChatMessage message, bool isGroupChat) async {
    try {
      final notification = {
        'type': 'new_message',
        'message': message.toJson(),
        'chat_id': message.senderId, // Use senderId as fallback
        'timestamp': DateTime.now().toIso8601String(),
      };

      if (isGroupChat) {
        // Send to bubble topic
        _mqttService.publish(
          'bubble/messages',
          jsonEncode(notification),
        );
      } else {
        // Send to user topic
        _mqttService.publish(
          'user/${message.senderId}/messages',
          jsonEncode(notification),
        );
      }
    } catch (e) {
      LoggingService.error('Error sending MQTT notification: $e');
    }
  }

  /// Send push notification via FCM
  Future<void> _sendPushNotification(ChatMessage message, bool isGroupChat) async {
    try {
      final title = isGroupChat
          ? 'New message in group'
          : 'New message';

      String body;
      switch (message.mediaType) {
        case MediaType.text:
          body = message.content;
          break;
        case MediaType.image:
          body = '📷 Image';
          break;
        case MediaType.video:
          body = '🎥 Video';
          break;
        case MediaType.audio:
          body = '🎵 Audio';
          break;
        case MediaType.file:
          body = '📄 Document';
          break;
        case MediaType.link:
          body = '🔗 Link';
          break;
        default:
          body = 'New message';
      }

      // TODO: Implement push notification sending with NotificationServiceFCM
      // The previous FcmService had sendToBubble/sendToUser methods that need to be reimplemented
      // For now, commenting out to allow compilation
      /*
      if (isGroupChat) {
        // Send to bubble members
        await _fcmService.sendToBubble(
          'group_chat', // Use a default group identifier
          title,
          body,
          data: {
            'type': 'message',
            'chatId': 'group_chat',
            'messageId': message.id,
            'senderId': message.senderId,
          },
          apiService: _apiService,
        );
      } else {
        // Send to specific user (this would need recipient token)
        // await _fcmService.sendToUser(recipientId, title, body, data);
      }
      */
    } catch (e) {
      LoggingService.error('Error sending push notification: $e');
    }
  }

  /// Forward a message to another chat
  Future<ChatMessage> forwardMessage({
    required String originalMessageId,
    required String targetChatId,
    required bool isGroupChat,
    String? additionalComment,
  }) async {
    try {
      // Get original message
      final originalMessage = await _getMessageById(originalMessageId);
      if (originalMessage == null) {
        throw MessagingException('Original message not found');
      }

      // Create forwarded message
      final forwardedContent = additionalComment != null
          ? '$additionalComment\n\n--- Forwarded Message ---\n${originalMessage.content}'
          : '--- Forwarded Message ---\n${originalMessage.content}';

      final messageModel = SendMessageModel(
        chatId: targetChatId,
        content: forwardedContent,
        messageType: originalMessage.mediaType,
        mediaUrl: originalMessage.mediaUrl,
        forwardedFromMessageId: originalMessageId,
      );

      return await _sendMessage(messageModel, isGroupChat);
    } catch (e) {
      throw MessagingException('Failed to forward message: $e');
    }
  }

  /// Get a message by ID (from cache or API)
  Future<ChatMessage?> _getMessageById(String messageId) async {
    // First check cache
    for (final messages in _messageCache.values) {
      final message = messages.where((msg) => msg.id == messageId).firstOrNull;
      if (message != null) return message;
    }

    // If not in cache, fetch from API
    try {
      final response = await _apiService.get('/chat/messages/$messageId');
      final apiMessage = ApiChatMessage.fromJson(response as Map<String, dynamic>);
      return ChatMessage(
        id: apiMessage.id,
        content: apiMessage.content,
        senderId: apiMessage.senderId,
        timestamp: apiMessage.createdAt,
        mediaType: _parseMediaType(apiMessage.messageType),
        isMe: false,
        isRead: false,
        isDelivered: true,
      );
    } catch (e) {
      LoggingService.error('Error fetching message by ID: $e');
      return null;
    }
  }

  /// Mark message as read
  Future<void> markMessageAsRead(String messageId) async {
    try {
      await _apiService.markMessageAsRead(messageId);
      
      // Send status update via MQTT
      final statusUpdate = {
        'type': 'message_status_update',
        'messageId': messageId,
        'status': 'read',
        'timestamp': DateTime.now().toIso8601String(),
      };
      
      // This would send to the message sender
      // await _mqttService.publishToUser(senderId, jsonEncode(statusUpdate));
    } catch (e) {
      LoggingService.error('Error marking message as read: $e');
    }
  }

  /// Get cached messages for a chat
  List<ChatMessage> getCachedMessages(String chatId) => _messageCache[chatId] ?? [];

  /// Clear message cache
  void clearCache() {
    _messageCache.clear();
  }

  /// Parse media type from string to enum
  MediaType _parseMediaType(String? typeString) {
    switch (typeString?.toLowerCase()) {
      case 'text':
        return MediaType.text;
      case 'image':
        return MediaType.image;
      case 'video':
        return MediaType.video;
      case 'audio':
        return MediaType.audio;
      case 'file':
      case 'document':
        return MediaType.file;
      case 'link':
        return MediaType.link;
      default:
        return MediaType.text;
    }
  }

  /// Dispose of resources
  void dispose() {
    _newMessageController.close();
    _messageStatusController.close();
  }
}

class MessagingException implements Exception {
  MessagingException(this.message);
  final String message;
  
  @override
  String toString() => 'MessagingException: $message';
} 