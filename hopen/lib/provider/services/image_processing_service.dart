import 'dart:io';
import 'dart:isolate';
import 'dart:typed_data';

import 'package:flutter/foundation.dart';
import 'package:flutter_image_compress/flutter_image_compress.dart';
import 'package:image/image.dart' as img;
import 'package:image_picker/image_picker.dart';
import 'package:path/path.dart' as path;

import '../exceptions/storage_exception.dart';

/// Comprehensive image processing service following industry best practices
/// Handles image selection, validation, processing, and compression with platform-specific logic
/// Android: HEIF/HEIC → WebP conversion, iOS: keeps native HEIF/HEIC format
/// This prevents UI blocking during intensive image operations
class ImageProcessingService {
  const ImageProcessingService._();

  // Constants following the profile picture pipeline requirements
  static const int maxFileSizeBytes = 2 * 1024 * 1024; // 2MB as per pipeline specs
  static const int minImageDimension = 640; // Minimum 640x640 as per requirements
  static const int maxImageDimension = 4096;
  static const int targetDimension = 1440; // Client-side resize target: 1440x1440 pixels max
  static const List<String> allowedExtensions = ['jpg', 'jpeg', 'png', 'webp', 'heif', 'heic'];
  static const List<String> allowedMimeTypes = [
    'image/jpeg',
    'image/jpg',
    'image/png',
    'image/webp',
    'image/heif',
    'image/heic'
  ];

  /// Platform detection for HEIF/HEIC processing logic
  static bool get _isAndroid => !kIsWeb && Platform.isAndroid;
  static bool get _isIOS => !kIsWeb && Platform.isIOS;

  /// Pick image from gallery with comprehensive validation
  static Future<XFile?> pickFromGallery() async {
    try {
      final ImagePicker picker = ImagePicker();
      final XFile? image = await picker.pickImage(
        source: ImageSource.gallery,
        maxWidth: targetDimension.toDouble(),
        maxHeight: targetDimension.toDouble(),
        imageQuality: 90, // Initial quality for picker
        preferredCameraDevice: CameraDevice.rear,
      );

      if (image == null) return null;

      // Validate the selected image
      await _validateImageFile(image);

      return image;
    } catch (e) {
      if (e is StorageException) rethrow;
      throw StorageException.processingFailed(
        reason: 'Failed to select image from gallery: $e',
      );
    }
  }

  /// Take photo with camera with comprehensive validation
  static Future<XFile?> takePhoto() async {
    try {
      final ImagePicker picker = ImagePicker();
      final XFile? image = await picker.pickImage(
        source: ImageSource.camera,
        maxWidth: targetDimension.toDouble(),
        maxHeight: targetDimension.toDouble(),
        imageQuality: 90, // Initial quality for picker
        preferredCameraDevice: CameraDevice.front, // Front camera for selfies
      );

      if (image == null) return null;

      // Validate the captured image
      await _validateImageFile(image);

      return image;
    } catch (e) {
      if (e is StorageException) rethrow;
      throw StorageException.processingFailed(
        reason: 'Failed to capture photo: $e',
      );
    }
  }

  /// Comprehensive image validation
  static Future<void> _validateImageFile(XFile imageFile) async {
    // Check file size
    final fileSize = await imageFile.length();
    if (fileSize > maxFileSizeBytes) {
      throw StorageException.fileTooLarge(
        maxSizeBytes: maxFileSizeBytes,
        actualSizeBytes: fileSize,
      );
    }

    // Check file extension
    final extension = path.extension(imageFile.path).toLowerCase();
    final cleanExtension = extension.startsWith('.') ? extension.substring(1) : extension;
    if (!allowedExtensions.contains(cleanExtension)) {
      throw StorageException.invalidFormat(
        actualFormat: cleanExtension,
        allowedFormats: allowedExtensions,
      );
    }

    // Read file and validate image format
    final imageBytes = await imageFile.readAsBytes();
    final image = img.decodeImage(imageBytes);

    if (image == null) {
      throw StorageException.corruptedFile();
    }

    // Check image dimensions (basic validation - backend will handle resizing)
    if (!_validateBasicRequirements(image)) {
      final width = image.width;
      final height = image.height;
      throw StorageException.invalidDimensions(
        width: width,
        height: height,
        minDimension: minImageDimension,
        maxDimension: maxImageDimension,
      );
    }
  }

  /// Process image with client-side WebP conversion:
  /// - WebP files: Keep as WebP, resize to 1440x1440, send to backend
  /// - All other formats (JPEG, PNG, HEIF, HEIC): Convert to WebP, resize to 1440x1440, send to backend
  /// - Backend: Receives WebP files and stores them as-is
  static Future<Uint8List> processImageFromFile(XFile imageFile) async {
    try {
      final extension = path.extension(imageFile.path).toLowerCase();
      final isWebP = extension == '.webp';

      // Convert all non-WebP formats to WebP using flutter_image_compress
      Uint8List webpBytes;

      if (isWebP) {
        // File is already WebP, just read the bytes
        print('📷 File is already WebP, skipping conversion');
        webpBytes = await imageFile.readAsBytes();
      } else {
        // Convert any other format (JPEG, PNG, HEIF, HEIC) to WebP
        print('🔄 Converting ${extension.toUpperCase()} to WebP...');
        try {
          final convertedBytes = await FlutterImageCompress.compressWithFile(
            imageFile.path,
            format: CompressFormat.webp,
            quality: 90, // High quality for conversion
            minWidth: targetDimension,
            minHeight: targetDimension,
          );

          if (convertedBytes == null) {
            throw StorageException.processingFailed(
              reason: 'Failed to convert ${extension.toUpperCase()} to WebP format',
            );
          }

          webpBytes = convertedBytes;
          print('✅ ${extension.toUpperCase()} → WebP conversion completed');
        } on UnsupportedError catch (e) {
          // Handle devices that don't support the format (e.g., HEIF/HEIC on Android < 10)
          throw StorageException.processingFailed(
            reason: 'Image format ${extension.toUpperCase()} is not supported on this device. Please use JPEG, PNG, or WebP format.',
          );
        } catch (e) {
          throw StorageException.processingFailed(
            reason: 'Failed to process ${extension.toUpperCase()} image: ${e.toString()}',
          );
        }
      }

      // Get original file size for quality calculation
      final originalSize = webpBytes.length;

      // Decode WebP image for validation
      final image = img.decodeImage(webpBytes);
      if (image == null) {
        throw StorageException.corruptedFile();
      }

      // Validate image meets requirements
      if (!_validateBasicRequirements(image)) {
        throw StorageException.invalidDimensions(
          width: image.width,
          height: image.height,
          minDimension: minImageDimension,
          maxDimension: maxImageDimension,
        );
      }

      // Remove EXIF data for privacy (client-side security)
      image.exif.clear();

      // Check if additional resizing is needed (flutter_image_compress may have already resized)
      final needsResizing = image.width > targetDimension || image.height > targetDimension;
      final Uint8List finalBytes;

      if (needsResizing) {
        // Additional client-side resizing to ensure exact square format
        print('📐 Additional resizing needed: ${image.width}x${image.height} → ${targetDimension}x${targetDimension}');
        final resizedImage = _resizeToSquare(image, targetDimension);

        // Determine compression quality based on original file size
        final quality = _calculateCompressionQuality(originalSize);

        // Re-encode as WebP using flutter_image_compress for better quality
        final tempDir = Directory.systemTemp;
        final tempFile = File('${tempDir.path}/temp_resize_${DateTime.now().millisecondsSinceEpoch}.jpg');
        await tempFile.writeAsBytes(img.encodeJpg(resizedImage, quality: 95));

        final recompressedBytes = await FlutterImageCompress.compressWithFile(
          tempFile.path,
          format: CompressFormat.webp,
          quality: quality,
          minWidth: targetDimension,
          minHeight: targetDimension,
        );

        // Clean up temp file
        await tempFile.delete();

        if (recompressedBytes == null) {
          throw StorageException.processingFailed(reason: 'Failed to recompress resized image');
        }

        finalBytes = recompressedBytes;
        print('✅ Additional resizing and WebP compression completed');
      } else {
        // Image is already the right size, use the WebP bytes as-is
        finalBytes = webpBytes;
        print('✅ Image already properly sized, using WebP as-is');
      }

      print('📊 Client-side processing: ${(originalSize / 1024 / 1024).toStringAsFixed(2)}MB → '
            '${(finalBytes.length / 1024 / 1024).toStringAsFixed(2)}MB');
      print('📐 Final dimensions: ${image.width}x${image.height} pixels');
      print('🎯 Backend will receive WebP format and store as-is');

      return finalBytes;
    } catch (e) {
      if (e is StorageException) rethrow;
      throw StorageException.processingFailed(
        reason: 'Failed to process image: $e',
      );
    }
  }

  /// Validate image meets basic requirements
  static bool _validateBasicRequirements(img.Image image) {
    final width = image.width;
    final height = image.height;

    // Ensure minimum dimensions for quality (640x640)
    if (width < minImageDimension || height < minImageDimension) {
      return false;
    }

    // Ensure not excessively large
    if (width > maxImageDimension || height > maxImageDimension) {
      return false;
    }

    return true;
  }

  /// Resize image to square format with maximum dimension of targetSize
  /// Crops to square first, then resizes if needed
  static img.Image _resizeToSquare(img.Image image, int targetSize) {
    final width = image.width;
    final height = image.height;

    // First, crop to square (center crop)
    final minDimension = width < height ? width : height;
    final cropX = (width - minDimension) ~/ 2;
    final cropY = (height - minDimension) ~/ 2;

    img.Image squareImage = img.copyCrop(
      image,
      x: cropX,
      y: cropY,
      width: minDimension,
      height: minDimension,
    );

    // Then resize if larger than target
    if (minDimension > targetSize) {
      squareImage = img.copyResize(
        squareImage,
        width: targetSize,
        height: targetSize,
        interpolation: img.Interpolation.cubic,
      );
    }

    return squareImage;
  }

  /// Calculate compression quality based on file size following the pipeline requirements
  /// <1MB: no compression (100% quality)
  /// 1-2MB: 90% quality compression
  /// >2MB: 80% quality compression (but files >2MB are rejected at validation)
  static int _calculateCompressionQuality(int fileSizeBytes) {
    const oneMB = 1024 * 1024;
    const twoMB = 2 * 1024 * 1024;

    if (fileSizeBytes < oneMB) {
      return 100; // No compression for files < 1MB
    } else if (fileSizeBytes <= twoMB) {
      return 90; // 90% quality compression for files 1-2MB
    } else {
      return 80; // 80% quality compression for files > 2MB (though these should be rejected)
    }
  }

  /// Process an image file with WebP as primary format (legacy method for backward compatibility)
  static Future<ImageProcessingResult> processImageInIsolate({
    required String imagePath,
    required int minResolution,
    required int maxResolution,
    required int maxFileSizeBytes,
    required double compressionQuality,
  }) async {
    try {
      print('🔄 Starting image processing...');

      // Create XFile from path for validation
      final imageFile = XFile(imagePath);

      // Validate the image
      await _validateImageFile(imageFile);

      // Process the image
      final processedBytes = await processImageFromFile(imageFile);

      return ImageProcessingResult.success(processedBytes);
    } on StorageException catch (e) {
      return ImageProcessingResult.error(e.message);
    } catch (e) {
      return ImageProcessingResult.error('Failed to process image: $e');
    }
  }

  /// Get image info without processing
  static Future<ImageInfo> getImageInfo(String imagePath) async {
    try {
      final file = File(imagePath);
      if (!await file.exists()) {
        throw StorageException.fileNotFound(fileName: path.basename(imagePath));
      }

      final imageBytes = await file.readAsBytes();
      final image = img.decodeImage(imageBytes);

      if (image == null) {
        throw StorageException.corruptedFile();
      }

      return ImageInfo(
        width: image.width,
        height: image.height,
        fileSizeBytes: imageBytes.length,
        format: _detectImageFormat(imagePath),
      );
    } catch (e) {
      if (e is StorageException) rethrow;
      throw StorageException.processingFailed(
        reason: 'Failed to get image info: $e',
      );
    }
  }

  /// Detect image format from file extension
  static String _detectImageFormat(String imagePath) {
    final extension = path.extension(imagePath).toLowerCase();
    switch (extension) {
      case '.jpg':
      case '.jpeg':
        return 'JPEG';
      case '.png':
        return 'PNG';
      case '.webp':
        return 'WebP';
      case '.heif':
        return 'HEIF';
      case '.heic':
        return 'HEIC';
      default:
        return 'Unknown';
    }
  }

}

/// Image information class
class ImageInfo {
  final int width;
  final int height;
  final int fileSizeBytes;
  final String format;

  const ImageInfo({
    required this.width,
    required this.height,
    required this.fileSizeBytes,
    required this.format,
  });

  double get fileSizeMB => fileSizeBytes / (1024 * 1024);

  bool get isSquare => width == height;

  bool get isValidDimensions =>
      width >= ImageProcessingService.minImageDimension &&
      height >= ImageProcessingService.minImageDimension &&
      width <= ImageProcessingService.maxImageDimension &&
      height <= ImageProcessingService.maxImageDimension;
}

/// Result of image processing operation
class ImageProcessingResult {
  const ImageProcessingResult._({
    required this.isSuccess,
    this.processedBytes,
    this.errorMessage,
  });

  factory ImageProcessingResult.success(Uint8List processedBytes) {
    return ImageProcessingResult._(
      isSuccess: true,
      processedBytes: processedBytes,
    );
  }

  factory ImageProcessingResult.error(String errorMessage) {
    return ImageProcessingResult._(
      isSuccess: false,
      errorMessage: errorMessage,
    );
  }

  final bool isSuccess;
  final Uint8List? processedBytes;
  final String? errorMessage;
}
