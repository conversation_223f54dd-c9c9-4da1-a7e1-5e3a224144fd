import 'dart:io';
import 'dart:isolate';
import 'dart:typed_data';

import 'package:flutter/foundation.dart';
import 'package:heif_converter/heif_converter.dart';
import 'package:image/image.dart' as img;
import 'package:image_picker/image_picker.dart';
import 'package:path/path.dart' as path;

import '../exceptions/storage_exception.dart';

/// Comprehensive image processing service following industry best practices
/// Handles image selection, validation, processing, and compression
/// This prevents UI blocking during intensive image operations
class ImageProcessingService {
  const ImageProcessingService._();

  // Constants following the profile picture pipeline requirements
  static const int maxFileSizeBytes = 2 * 1024 * 1024; // 2MB as per pipeline specs
  static const int minImageDimension = 100;
  static const int maxImageDimension = 4096;
  static const int targetDimension = 1440; // Standard profile picture size (backend resizes to this)
  static const List<String> allowedExtensions = ['jpg', 'jpeg', 'png', 'webp', 'heif', 'heic'];
  static const List<String> allowedMimeTypes = [
    'image/jpeg',
    'image/jpg',
    'image/png',
    'image/webp',
    'image/heif',
    'image/heic'
  ];

  /// Pick image from gallery with comprehensive validation
  static Future<XFile?> pickFromGallery() async {
    try {
      final ImagePicker picker = ImagePicker();
      final XFile? image = await picker.pickImage(
        source: ImageSource.gallery,
        maxWidth: targetDimension.toDouble(),
        maxHeight: targetDimension.toDouble(),
        imageQuality: 90, // Initial quality for picker
        preferredCameraDevice: CameraDevice.rear,
      );

      if (image == null) return null;

      // Validate the selected image
      await _validateImageFile(image);

      return image;
    } catch (e) {
      if (e is StorageException) rethrow;
      throw StorageException.processingFailed(
        reason: 'Failed to select image from gallery: $e',
      );
    }
  }

  /// Take photo with camera with comprehensive validation
  static Future<XFile?> takePhoto() async {
    try {
      final ImagePicker picker = ImagePicker();
      final XFile? image = await picker.pickImage(
        source: ImageSource.camera,
        maxWidth: targetDimension.toDouble(),
        maxHeight: targetDimension.toDouble(),
        imageQuality: 90, // Initial quality for picker
        preferredCameraDevice: CameraDevice.front, // Front camera for selfies
      );

      if (image == null) return null;

      // Validate the captured image
      await _validateImageFile(image);

      return image;
    } catch (e) {
      if (e is StorageException) rethrow;
      throw StorageException.processingFailed(
        reason: 'Failed to capture photo: $e',
      );
    }
  }

  /// Comprehensive image validation
  static Future<void> _validateImageFile(XFile imageFile) async {
    // Check file size
    final fileSize = await imageFile.length();
    if (fileSize > maxFileSizeBytes) {
      throw StorageException.fileTooLarge(
        maxSizeBytes: maxFileSizeBytes,
        actualSizeBytes: fileSize,
      );
    }

    // Check file extension
    final extension = path.extension(imageFile.path).toLowerCase();
    final cleanExtension = extension.startsWith('.') ? extension.substring(1) : extension;
    if (!allowedExtensions.contains(cleanExtension)) {
      throw StorageException.invalidFormat(
        actualFormat: cleanExtension,
        allowedFormats: allowedExtensions,
      );
    }

    // Read file and validate image format
    final imageBytes = await imageFile.readAsBytes();
    final image = img.decodeImage(imageBytes);

    if (image == null) {
      throw StorageException.corruptedFile();
    }

    // Check image dimensions (basic validation - backend will handle resizing)
    if (!_validateBasicRequirements(image)) {
      final width = image.width;
      final height = image.height;
      throw StorageException.invalidDimensions(
        width: width,
        height: height,
        minDimension: minImageDimension,
        maxDimension: maxImageDimension,
      );
    }
  }

  /// Process image following the pipeline: client handles basic processing, backend handles WebP conversion
  /// Client responsibilities: validation, basic compression, format standardization to JPEG
  /// Backend responsibilities: resize to 1440x1440, convert to WebP, store and serve
  static Future<Uint8List> processImageFromFile(XFile imageFile) async {
    try {
      // Check if the file is HEIF/HEIC and convert to JPEG first
      final extension = path.extension(imageFile.path).toLowerCase();
      XFile processedFile = imageFile;

      if (extension == '.heif' || extension == '.heic') {
        print('🔄 Converting HEIF/HEIC to JPEG...');
        final convertedPath = await HeifConverter.convert(
          imageFile.path,
          format: 'jpg',
        );

        if (convertedPath == null) {
          throw StorageException.processingFailed(
            reason: 'Failed to convert HEIF/HEIC file',
          );
        }

        processedFile = XFile(convertedPath);
        print('✅ HEIF/HEIC conversion completed');
      }

      // Read image bytes
      final imageBytes = await processedFile.readAsBytes();
      final originalSize = imageBytes.length;

      // Decode image for validation and basic processing
      final image = img.decodeImage(imageBytes);
      if (image == null) {
        throw StorageException.corruptedFile();
      }

      // Remove EXIF data for privacy (client-side security)
      image.exif.clear();

      // Determine compression quality based on original file size
      final quality = _calculateCompressionQuality(originalSize);

      // Basic compression and format standardization to JPEG
      // Backend will handle resizing to 1440x1440 and WebP conversion
      final processedBytes = Uint8List.fromList(
        img.encodeJpg(image, quality: quality)
      );

      print('📊 Client-side processing: ${(originalSize / 1024 / 1024).toStringAsFixed(2)}MB → '
            '${(processedBytes.length / 1024 / 1024).toStringAsFixed(2)}MB (${quality}% quality)');
      print('🔄 Backend will resize to 1440x1440 and convert to WebP');

      return processedBytes;
    } catch (e) {
      if (e is StorageException) rethrow;
      throw StorageException.processingFailed(
        reason: 'Failed to process image: $e',
      );
    }
  }

  /// Validate image meets basic requirements (backend will handle resizing and cropping)
  static bool _validateBasicRequirements(img.Image image) {
    // Basic validation - backend will handle the actual processing
    final width = image.width;
    final height = image.height;

    // Ensure minimum dimensions for quality
    if (width < minImageDimension || height < minImageDimension) {
      return false;
    }

    // Ensure not excessively large (backend will resize anyway)
    if (width > maxImageDimension || height > maxImageDimension) {
      return false;
    }

    return true;
  }

  /// Calculate compression quality based on file size following the pipeline requirements
  /// Below 1MB: no compression, only conversion to WebP
  /// Above 1MB: 90% quality compression
  /// Above 2MB: 80% quality compression (but files > 2MB are rejected)
  static int _calculateCompressionQuality(int fileSizeBytes) {
    const oneMB = 1024 * 1024;

    if (fileSizeBytes < oneMB) {
      return 100; // No compression for files < 1MB, only conversion to WebP
    } else {
      return 90; // 90% quality compression for files 1-2MB
    }
  }

  /// Process an image file with WebP as primary format (legacy method for backward compatibility)
  static Future<ImageProcessingResult> processImageInIsolate({
    required String imagePath,
    required int minResolution,
    required int maxResolution,
    required int maxFileSizeBytes,
    required double compressionQuality,
  }) async {
    try {
      print('🔄 Starting image processing...');

      // Create XFile from path for validation
      final imageFile = XFile(imagePath);

      // Validate the image
      await _validateImageFile(imageFile);

      // Process the image
      final processedBytes = await processImageFromFile(imageFile);

      return ImageProcessingResult.success(processedBytes);
    } on StorageException catch (e) {
      return ImageProcessingResult.error(e.message);
    } catch (e) {
      return ImageProcessingResult.error('Failed to process image: $e');
    }
  }

  /// Get image info without processing
  static Future<ImageInfo> getImageInfo(String imagePath) async {
    try {
      final file = File(imagePath);
      if (!await file.exists()) {
        throw StorageException.fileNotFound(fileName: path.basename(imagePath));
      }

      final imageBytes = await file.readAsBytes();
      final image = img.decodeImage(imageBytes);

      if (image == null) {
        throw StorageException.corruptedFile();
      }

      return ImageInfo(
        width: image.width,
        height: image.height,
        fileSizeBytes: imageBytes.length,
        format: _detectImageFormat(imagePath),
      );
    } catch (e) {
      if (e is StorageException) rethrow;
      throw StorageException.processingFailed(
        reason: 'Failed to get image info: $e',
      );
    }
  }

  /// Detect image format from file extension
  static String _detectImageFormat(String imagePath) {
    final extension = path.extension(imagePath).toLowerCase();
    switch (extension) {
      case '.jpg':
      case '.jpeg':
        return 'JPEG';
      case '.png':
        return 'PNG';
      case '.webp':
        return 'WebP';
      case '.heif':
        return 'HEIF';
      case '.heic':
        return 'HEIC';
      default:
        return 'Unknown';
    }
  }

}

/// Image information class
class ImageInfo {
  final int width;
  final int height;
  final int fileSizeBytes;
  final String format;

  const ImageInfo({
    required this.width,
    required this.height,
    required this.fileSizeBytes,
    required this.format,
  });

  double get fileSizeMB => fileSizeBytes / (1024 * 1024);

  bool get isSquare => width == height;

  bool get isValidDimensions =>
      width >= ImageProcessingService.minImageDimension &&
      height >= ImageProcessingService.minImageDimension &&
      width <= ImageProcessingService.maxImageDimension &&
      height <= ImageProcessingService.maxImageDimension;
}

/// Result of image processing operation
class ImageProcessingResult {
  const ImageProcessingResult._({
    required this.isSuccess,
    this.processedBytes,
    this.errorMessage,
  });

  factory ImageProcessingResult.success(Uint8List processedBytes) {
    return ImageProcessingResult._(
      isSuccess: true,
      processedBytes: processedBytes,
    );
  }

  factory ImageProcessingResult.error(String errorMessage) {
    return ImageProcessingResult._(
      isSuccess: false,
      errorMessage: errorMessage,
    );
  }

  final bool isSuccess;
  final Uint8List? processedBytes;
  final String? errorMessage;
}
