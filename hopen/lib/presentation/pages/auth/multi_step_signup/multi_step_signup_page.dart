import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:go_router/go_router.dart';
import 'package:flutter/material.dart';

import '../../../../statefulbusinesslogic/bloc/auth/auth_bloc.dart';
import '../../../../statefulbusinesslogic/bloc/auth/auth_event.dart';
import '../../../../statefulbusinesslogic/bloc/signup/signup_bloc.dart';
import '../../../router/app_router.dart';
import '../../../widgets/custom_toast.dart';
import '../../../../provider/services/auth/ory_auth_service.dart';
import '../../../../di/injection_container_refactored.dart' as di;
import 'steps/step1_name_page.dart';
import 'steps/step2_password_page.dart';
import 'steps/step3_username_birthday_page.dart';
import 'steps/step4_email_verification_page.dart';
import 'steps/step5_profile_picture_page.dart';
import 'steps/step6_notifications_page.dart';

class MultiStepSignupPage extends StatefulWidget {
  const MultiStepSignupPage({super.key});

  @override
  State<MultiStepSignupPage> createState() => _MultiStepSignupPageState();
}

class _MultiStepSignupPageState extends State<MultiStepSignupPage> {
  // Current step index (0-based)
  int _currentStep = 0;

  // Total number of steps
  static const int _totalSteps = 6;

  // Form data
  final _firstNameController = TextEditingController();
  final _lastNameController = TextEditingController();
  final _passwordController = TextEditingController();
  final _confirmPasswordController = TextEditingController();
  final _usernameController = TextEditingController();
  final _emailController = TextEditingController();
  final _birthdayController = TextEditingController();
  final _verificationCodeController = TextEditingController();
  String? _selectedProfilePicturePath;
  bool _notificationsEnabled = true;

  @override
  void initState() {
    super.initState();
    // Clear any existing session when starting signup to prevent
    // profile picture uploads from being associated with wrong user
    _clearExistingSession();
  }

  /// Clear any existing authentication session to prevent cross-user contamination
  Future<void> _clearExistingSession() async {
    try {
      final oryAuthService = di.sl<OryAuthService>();
      await oryAuthService.signOut();
      print('🔄 MultiStepSignupPage: Cleared existing session for new signup');
    } catch (e) {
      print('⚠️ MultiStepSignupPage: Failed to clear existing session: $e');
      // Continue with signup even if session clearing fails
    }
  }

  @override
  void dispose() {
    _firstNameController.dispose();
    _lastNameController.dispose();
    _passwordController.dispose();
    _confirmPasswordController.dispose();
    _usernameController.dispose();
    _emailController.dispose();
    _birthdayController.dispose();
    _verificationCodeController.dispose();
    super.dispose();
  }

  // Move to the next step
  void _nextStep([dynamic data]) {
    if (_currentStep < _totalSteps - 1) {
      if (_currentStep == 4 && data is String?) {
        _selectedProfilePicturePath = data;
      }
      if (_currentStep == 5 && data is bool) _notificationsEnabled = data;

      setState(() {
        _currentStep++;
      });
    } else {
      if (_currentStep == 5 && data is bool) _notificationsEnabled = data;
      _completeSignup();
    }
  }

  // Move to the previous step
  void _previousStep() {
    if (_currentStep > 0) {
      setState(() {
        _currentStep--;
      });
    }
  }

  // Complete signup and navigate to profile
  void _completeSignup() {
    DateTime? birthdayDate;
    if (_birthdayController.text.isNotEmpty) {
      try {
        final parts = _birthdayController.text.split('/');
        if (parts.length == 3) {
          birthdayDate = DateTime(
            int.parse(parts[2]),
            int.parse(parts[1]),
            int.parse(parts[0]),
          );
        }
      } catch (e) {
        print('Error parsing birthday: $e');
      }
    }

    context.read<SignUpBloc>().add(
      SignUpSubmitted(
        firstName: _firstNameController.text,
        lastName: _lastNameController.text,
        email: _emailController.text,
        password: _passwordController.text,
        username: _usernameController.text,
        birthday: birthdayDate,
        profilePicturePathOrUrl: _selectedProfilePicturePath,
        notificationsEnabled: _notificationsEnabled,
      ),
    );
  }

  @override
  Widget build(BuildContext context) => BlocListener<SignUpBloc, SignUpState>(
      listener: (context, state) {
        if (state is SignUpSuccess) {
          context.read<AuthBloc>().add(UserLoggedInEvent(state.user));

          // Navigate to onboarding page
          context.go(AppRoutes.onboarding);

          var welcomeName = state.user.firstName;
          if (welcomeName == null || welcomeName.isEmpty) {
            welcomeName = state.user.username ?? 'User';
          }
          CustomToast.showSuccess(context, 'Account created for $welcomeName!');
        } else if (state is SignUpPartialSuccess) {
          // Account created but authentication failed - redirect to login
          var welcomeName = state.user.firstName;
          if (welcomeName == null || welcomeName.isEmpty) {
            welcomeName = state.user.username ?? 'User';
          }

          CustomToast.showInfo(context, 'Account created for $welcomeName! Please login to continue.');

          // Navigate to login page
          context.go(AppRoutes.login);
        } else if (state is SignUpFailure) {
          CustomToast.showError(context, 'Signup Failed: ${state.message}');
        }
      },
      child: Scaffold(
        resizeToAvoidBottomInset: false,
        backgroundColor: const Color(0xFF0A2955),
        body: _buildStepPage(),
      ),
    );

  Widget _buildStepPage() {
    switch (_currentStep) {
      case 0:
        return Step1NamePage(
          currentStep: _currentStep + 1,
          totalSteps: _totalSteps,
          onNext: _nextStep,
          firstNameController: _firstNameController,
          lastNameController: _lastNameController,
        );

      case 1:
        return Step2PasswordPage(
          currentStep: _currentStep + 1,
          totalSteps: _totalSteps,
          onNext: _nextStep,
          onBack: _previousStep,
          passwordController: _passwordController,
          confirmPasswordController: _confirmPasswordController,
        );

      case 2:
        return Step3UsernameBirthdayPage(
          currentStep: _currentStep + 1,
          totalSteps: _totalSteps,
          onNext: _nextStep,
          onBack: _previousStep,
          usernameController: _usernameController,
          emailController: _emailController,
          birthdayController: _birthdayController,
        );

      case 3:
        return Step4EmailVerificationPage(
          currentStep: _currentStep + 1,
          totalSteps: _totalSteps,
          onNext: _nextStep,
          onBack: _previousStep,
          verificationCodeController: _verificationCodeController,
        );

      case 4:
        return Step5ProfilePicturePage(
          currentStep: _currentStep + 1,
          totalSteps: _totalSteps,
          onNextCustom: _nextStep,
          onBack: _previousStep,
          initialImageUrl: _selectedProfilePicturePath,
        );

      case 5:
        return Step6NotificationsPage(
          currentStep: _currentStep + 1,
          totalSteps: _totalSteps,
          onNextCustom: _nextStep,
          onBack: _previousStep,
          initialValue: _notificationsEnabled,
        );

      default:
        return Container(); // Should never happen
    }
  }
}
