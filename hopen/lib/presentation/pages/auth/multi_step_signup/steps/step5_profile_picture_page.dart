import 'dart:io'; // For File

import 'package:flutter/material.dart';
 // Added for image picking

import '../../../../../statefulbusinesslogic/bloc/profile_picture/profile_picture_bloc.dart';
import '../../../../../statefulbusinesslogic/bloc/profile_picture/profile_picture_event.dart';
import '../../../../../statefulbusinesslogic/bloc/profile_picture/profile_picture_state.dart';
import '../../../../../di/injection_container_refactored.dart' as di;
import '../../../../widgets/custom_toast.dart';
import '../../../../widgets/profile_picture_widget.dart';
import '../signup_step_base.dart';

class Step5ProfilePicturePage extends SignupStepBase {

  Step5ProfilePicturePage({
    required super.currentStep,
    required super.totalSteps,
    required this.onNextCustom,
    required VoidCallback super.onBack,
    this.initialImageUrl,
    super.key,
  }) : super(
         title: 'Choose your profile picture',
         onNext: () {},
       );
  final void Function(String?) onNextCustom;
  final String? initialImageUrl;

  @override
  State<Step5ProfilePicturePage> createState() =>
      _Step5ProfilePicturePageState();
}

class _Step5ProfilePicturePageState
    extends SignupStepBaseState<Step5ProfilePicturePage> {
  File? _selectedImageFile;
  String? _uploadedImageUrl;
  bool _isUploadingImage = false;
  late ProfilePictureBloc _profilePictureBloc;

  @override
  void initState() {
    super.initState();
    _profilePictureBloc = di.sl<ProfilePictureBloc>();

    // Restore previously selected image (if provided)
    _uploadedImageUrl = widget.initialImageUrl;

    // Listen to profile picture state changes
    _profilePictureBloc.stream.listen((state) {
      if (!mounted) return;

      if (state is ProfilePictureLoading) {
        setState(() {
          _isUploadingImage = true;
        });
      } else {
        setState(() {
          _isUploadingImage = false;
        });

        if (state is ProfilePictureSuccess) {
          setState(() {
            _uploadedImageUrl = state.result.url;
            _selectedImageFile = null;
          });

          CustomToast.showSuccess(
            context,
            state.message ?? 'Profile picture uploaded successfully!',
          );
        } else if (state is ProfilePictureError) {
          // Enhanced error handling with retry option
          _showErrorDialog(state);
        }
      }
    });
  }

  @override
  void dispose() {
    _profilePictureBloc.close();
    super.dispose();
  }

  @override
  bool isFormValid() => true;

  // This handleNext is called by the button in SignupStepBase
  @override
  void handleNext() {
    if (isFormValid()) {
      // Pass the uploaded image URL instead of local file path
      widget.onNextCustom(
        _uploadedImageUrl,
      );
    } else {
      // Show custom toast when validation fails
      CustomToast.showError(
        context, 
        'Please enter all your infos to continue',
      );
    }
  }

  @override
  List<Widget> buildStepContent(BuildContext context) {
    final screenHeight = MediaQuery.of(context).size.height;
    final spacingHeight = screenHeight / 48;
    final fieldHeight = screenHeight / 16;
    final avatarSize = screenHeight * 0.15;

    return [
      // Image requirements text – identical style and spacing to Step 4 notice
      Text(
        'Your image should be at least 640x640 pixels, in JPEG, PNG, WebP, HEIF or HEIC format.',
        style: TextStyle(
          color: Colors.white.withValues(alpha: 0.7),
          fontSize: 16,
          height: 1.5,
        ),
        textAlign: TextAlign.center,
      ),
      SizedBox(height: spacingHeight * 2),
      Center(
        child: GestureDetector(
          onTap: _isUploadingImage ? null : _selectImageOptions,
          child: _uploadedImageUrl != null || _selectedImageFile != null
              ? ProfilePictureWidget(
                  imageUrl: _uploadedImageUrl ?? _selectedImageFile?.path,
                  firstName: 'User',
                  lastName: 'Avatar',
                  radius: avatarSize / 2,
                )
              : Container(
                  width: avatarSize,
                  height: avatarSize,
                  decoration: ShapeDecoration(
                    shape: RoundedSuperellipseBorder(
                      borderRadius: BorderRadius.circular(avatarSize * 0.4),
                      side: BorderSide(color: Colors.blue, width: 2),
                    ),
                    color: Colors.white.withValues(alpha: 0.1),
                  ),
                  child: Icon(
                    Icons.add_a_photo,
                    color: Colors.white,
                    size: avatarSize * 0.4,
                  ),
                ),
        ),
      ),
      SizedBox(height: spacingHeight),
      SizedBox(
        height: fieldHeight,
        child: Row(
          children: [
            Expanded(
              child: _buildButton(
                label: 'Gallery',
                icon: Icons.photo_library,
                onPressed: _isUploadingImage ? null : () => _pickImage('gallery'),
              ),
            ),
            SizedBox(width: spacingHeight),
            Expanded(
              child: _buildButton(
                label: 'Camera',
                icon: Icons.camera_alt,
                onPressed: _isUploadingImage ? null : () => _pickImage('camera'),
              ),
            ),
          ],
        ),
      ),
      SizedBox(height: spacingHeight),
      // Skip for now text - matching Step 4 "Resend verification code" styling exactly
      TextButton(
        onPressed: _isUploadingImage 
            ? null 
            : () => widget.onNextCustom(null), // Call custom onNext for skip
        style: TextButton.styleFrom(
          padding: EdgeInsets.zero,
          tapTargetSize: MaterialTapTargetSize.shrinkWrap,
          alignment: Alignment.center,
        ),
        child: Text(
          'Skip for now',
          style: TextStyle(color: Colors.white.withValues(alpha: 0.7), fontSize: 16),
        ),
      ),
    ];
  }

  Widget _buildButton({
    required String label,
    required IconData icon,
    required VoidCallback? onPressed,
  }) {
    final screenHeight = MediaQuery.of(context).size.height;
    final fieldHeight = screenHeight / 16;

    return ElevatedButton.icon(
      onPressed: onPressed,
      style: ElevatedButton.styleFrom(
        backgroundColor: Colors.white.withValues(alpha: 0.1),
        foregroundColor: Colors.white,
        shape: RoundedSuperellipseBorder(
          borderRadius: BorderRadius.circular(18),
          side: BorderSide(color: Colors.white.withValues(alpha: 0.3)),
        ),
        padding: EdgeInsets.zero,
        minimumSize: Size.zero,
        maximumSize: const Size(double.infinity, double.infinity),
        tapTargetSize: MaterialTapTargetSize.shrinkWrap,
        fixedSize: Size.fromHeight(fieldHeight),
      ),
      icon: Icon(icon),
      label: Text(label),
    );
  }

  void _selectImageOptions() {
    showModalBottomSheet(
      context: context,
      backgroundColor: Colors.transparent,
      builder: (context) => Container(
        decoration: BoxDecoration(
          color: const Color(0xFF1E1E1E),
          borderRadius: const BorderRadius.vertical(top: Radius.circular(20)),
        ),
        child: SafeArea(
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              const SizedBox(height: 20),
              const Text(
                'Select Profile Picture',
                style: TextStyle(
                  color: Colors.white,
                  fontSize: 18,
                  fontWeight: FontWeight.w600,
                ),
              ),
              const SizedBox(height: 20),
              ListTile(
                leading: const Icon(Icons.camera_alt, color: Colors.white),
                title: const Text('Camera', style: TextStyle(color: Colors.white)),
                onTap: () {
                  Navigator.pop(context);
                  _pickImage('camera');
                },
              ),
              ListTile(
                leading: const Icon(Icons.photo_library, color: Colors.white),
                title: const Text('Gallery', style: TextStyle(color: Colors.white)),
                onTap: () {
                  Navigator.pop(context);
                  _pickImage('gallery');
                },
              ),
              if (_uploadedImageUrl != null)
                ListTile(
                  leading: const Icon(Icons.delete, color: Colors.red),
                  title: const Text('Remove Picture', style: TextStyle(color: Colors.red)),
                  onTap: () {
                    Navigator.pop(context);
                    _removeImage();
                  },
                ),
              const SizedBox(height: 20),
            ],
          ),
        ),
      ),
    );
  }

  Future<void> _pickImage(String source) async {
    // Let the BLoC handle the entire image picking and upload process
    if (source == 'camera') {
      _profilePictureBloc.add(const TakePhotoEvent());
    } else {
      _profilePictureBloc.add(const PickFromGalleryEvent());
    }
  }

  void _removeImage() {
    setState(() {
      _uploadedImageUrl = null;
      _selectedImageFile = null;
    });

    CustomToast.show(context, 'Profile picture removed');
  }

  void _showErrorDialog(ProfilePictureError state) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        backgroundColor: Theme.of(context).scaffoldBackgroundColor,
        shape: const RoundedSuperellipseBorder(
          borderRadius: BorderRadius.all(Radius.circular(18)),
        ),
        title: Row(
          children: [
            Icon(
              _getErrorIcon(state.errorType),
              color: _getErrorColor(state.errorType),
            ),
            const SizedBox(width: 8),
            const Text('Upload Failed'),
          ],
        ),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(state.message),
            if (state.technicalDetails != null && state.technicalDetails!.isNotEmpty) ...[
              const SizedBox(height: 8),
              ExpansionTile(
                title: const Text(
                  'Technical Details',
                  style: TextStyle(fontSize: 12, color: Colors.grey),
                ),
                children: [
                  Padding(
                    padding: const EdgeInsets.all(8.0),
                    child: Text(
                      state.technicalDetails!,
                      style: const TextStyle(fontSize: 10, color: Colors.grey),
                    ),
                  ),
                ],
              ),
            ],
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('OK'),
          ),
          if (state.isRetryable)
            ElevatedButton(
              onPressed: () {
                Navigator.of(context).pop();
                // Retry the last operation
                _profilePictureBloc.add(
                  RetryUploadEvent(
                    lastOperation: ProfilePictureOperation.pickFromGallery,
                  ),
                );
              },
              child: const Text('Retry'),
            ),
        ],
      ),
    );
  }

  IconData _getErrorIcon(ProfilePictureErrorType errorType) {
    switch (errorType) {
      case ProfilePictureErrorType.network:
        return Icons.wifi_off;
      case ProfilePictureErrorType.storage:
        return Icons.cloud_off;
      case ProfilePictureErrorType.fileSize:
        return Icons.file_present;
      case ProfilePictureErrorType.fileFormat:
        return Icons.image_not_supported;
      default:
        return Icons.error;
    }
  }

  Color _getErrorColor(ProfilePictureErrorType errorType) {
    switch (errorType) {
      case ProfilePictureErrorType.network:
        return Colors.orange;
      case ProfilePictureErrorType.storage:
        return Colors.red;
      case ProfilePictureErrorType.fileSize:
      case ProfilePictureErrorType.fileFormat:
        return Colors.amber;
      default:
        return Colors.red;
    }
  }
}
