import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:go_router/go_router.dart';

import '../../../statefulbusinesslogic/bloc/unified_profile/unified_profile_bloc.dart';
import '../../../statefulbusinesslogic/bloc/unified_profile/unified_profile_event.dart';
import '../../../statefulbusinesslogic/bloc/unified_profile/unified_profile_state.dart';
import '../../../statefulbusinesslogic/core/models/user_model.dart' as core_models;
import '../../widgets/gradient_background.dart';
import '../../widgets/shimmer_placeholders.dart';
import '../../widgets/custom_toast.dart';
import '../unified_profile_page/unified_profile_page.dart';

/// A page that displays a user's profile based on their username
/// This page is accessible via URLs like hopenapp.com/u/{username}
/// and supports both web and deep linking scenarios
class UsernameProfilePage extends StatefulWidget {
  final String username;

  const UsernameProfilePage({
    Key? key,
    required this.username,
  }) : super(key: key);

  @override
  State<UsernameProfilePage> createState() => _UsernameProfilePageState();
}

class _UsernameProfilePageState extends State<UsernameProfilePage> {
  @override
  void initState() {
    super.initState();
    // Load user profile by username
    context.read<UnifiedProfileBloc>().add(
      LoadUserProfileByUsernameEvent(widget.username),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: GradientBackground(
        child: BlocConsumer<UnifiedProfileBloc, UnifiedProfileState>(
          listener: (context, state) {
            if (state is UnifiedProfileError) {
              // Handle different error types
              if (state.message.contains('not found')) {
                _showUserNotFoundDialog();
              } else if (state.message.contains('private')) {
                _showPrivateProfileDialog();
              } else {
                CustomToast.showError(context, state.message);
              }
            }
          },
          builder: (context, state) {
            if (state is UnifiedProfileInitial || state is UnifiedProfileLoading) {
              return _buildLoadingState();
            } else if (state is UnifiedProfileLoaded) {
              // Once we have the user data, delegate to the existing UnifiedProfilePage
              return UnifiedProfilePage(
                userId: state.user.id,
                showCallActionButtons: false, // Disable call actions for public profiles
              );
            } else if (state is UnifiedProfileError) {
              return _buildErrorState(state.message);
            }
            
            return _buildErrorState('Something went wrong');
          },
        ),
      ),
    );
  }

  Widget _buildLoadingState() {
    return const Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          CircularProgressIndicator(
            valueColor: AlwaysStoppedAnimation<Color>(Color(0xFF00FFFF)),
          ),
          SizedBox(height: 16),
          Text(
            'Loading profile...',
            style: TextStyle(
              color: Colors.white,
              fontSize: 16,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildErrorState(String message) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          const Icon(
            Icons.error_outline,
            size: 64,
            color: Colors.white54,
          ),
          const SizedBox(height: 16),
          Text(
            'Profile Not Available',
            style: const TextStyle(
              color: Colors.white,
              fontSize: 24,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            message,
            style: const TextStyle(
              color: Colors.white70,
              fontSize: 16,
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 24),
          ElevatedButton(
            onPressed: () {
              // Try to go back, or go to login if no previous route
              if (context.canPop()) {
                context.pop();
              } else {
                context.go('/login');
              }
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: const Color(0xFF00FFFF),
              foregroundColor: Colors.black,
            ),
            child: const Text('Go Back'),
          ),
        ],
      ),
    );
  }

  void _showUserNotFoundDialog() {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          backgroundColor: const Color(0xFF1A1A1A),
          title: const Text(
            'User Not Found',
            style: TextStyle(color: Colors.white),
          ),
          content: Text(
            'The user @${widget.username} could not be found. They may have changed their username or deleted their account.',
            style: const TextStyle(color: Colors.white70),
          ),
          actions: [
            TextButton(
              onPressed: () {
                Navigator.of(context).pop();
                if (context.canPop()) {
                  context.pop();
                } else {
                  context.go('/login');
                }
              },
              child: const Text(
                'OK',
                style: TextStyle(color: Color(0xFF00FFFF)),
              ),
            ),
          ],
        );
      },
    );
  }

  void _showPrivateProfileDialog() {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          backgroundColor: const Color(0xFF1A1A1A),
          title: const Text(
            'Private Profile',
            style: TextStyle(color: Colors.white),
          ),
          content: Text(
            '@${widget.username} has a private profile. You need to be connected with them to view their full profile.',
            style: const TextStyle(color: Colors.white70),
          ),
          actions: [
            TextButton(
              onPressed: () {
                Navigator.of(context).pop();
                if (context.canPop()) {
                  context.pop();
                } else {
                  context.go('/login');
                }
              },
              child: const Text(
                'OK',
                style: TextStyle(color: Color(0xFF00FFFF)),
              ),
            ),
          ],
        );
      },
    );
  }
}
