import 'dart:async';
import 'dart:math' as math;
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:go_router/go_router.dart';
import 'package:provider/provider.dart';

import '../../di/injection_container_refactored.dart' as di;

import '../../statefulbusinesslogic/bloc/auth/auth_bloc.dart';
import '../../statefulbusinesslogic/bloc/auth/auth_event.dart';
import '../../statefulbusinesslogic/bloc/auth/auth_state.dart';
import '../../statefulbusinesslogic/bloc/bubble/bubble_bloc.dart';
import '../../statefulbusinesslogic/bloc/bubble/bubble_state.dart';
import '../../statefulbusinesslogic/bloc/call/call_bloc.dart';
import '../../statefulbusinesslogic/bloc/call/call_state.dart';
import '../../statefulbusinesslogic/bloc/unified_profile/unified_profile_bloc.dart';
import '../../statefulbusinesslogic/core/models/bubble_member.dart';
import '../../statefulbusinesslogic/core/notifiers/nav_bar_visibility_notifier.dart';
import '../pages/auth/create_new_password_page.dart';
import '../pages/auth/forgot_password_page.dart';
import '../pages/auth/login_page.dart';
import '../pages/auth/multi_step_signup/multi_step_signup_page.dart';
import '../pages/auth/reset_password_verification_page.dart';
import '../pages/bubble/bubble_page.dart';
import '../pages/call/bubble_call_page.dart';
import '../pages/call/friends_call_page.dart';
import '../pages/call/incoming_bubble_call_page.dart';
import '../pages/call/incoming_friend_call_page.dart';
import '../pages/contacts/contacts_page.dart';
import '../pages/friends/friends_page.dart';
import '../pages/onboarding/onboarding_data.dart';
import '../pages/onboarding/onboarding_page.dart';
import '../pages/profile/profile_page.dart';
import '../pages/unified_profile_page/unified_profile_page.dart';
import '../pages/username_profile_page/username_profile_page.dart';
import '../widgets/bubble_call_dialog.dart';
import '../widgets/hopen_button_demo_dialog.dart';
import '../../di/injection_container_refactored.dart';
import '../../statefulbusinesslogic/bloc/home_navigation/home_navigation_bloc.dart';

/// Route constants for the Hopen app
class AppRoutes {
  // Private constructor to prevent instantiation
  AppRoutes._();

  // Authentication routes
  static const String login = '/login';
  static const String multiStepSignup = '/multi-step-signup';
  static const String forgotPassword = '/forgot-password';
  static const String resetPasswordVerification = '/reset-password-verification';
  static const String createNewPassword = '/create-new-password';
  static const String resetPassword = '/reset-password';

  // Onboarding
  static const String onboarding = '/onboarding';

  // Main app routes
  static const String friends = '/friends';
  static const String profile = '/profile';
  static const String userProfile = '/user';
  static const String usernameProfile = '/u'; // New route for username-based profiles
  static const String bubble = '/bubble';
  static const String contacts = '/contacts';

  // Call routes (these are defined in their respective page classes)
  // static const String incomingFriendCall = '/incoming-friend-call';
  // static const String incomingBubbleCall = '/incoming-bubble-call';
  // static const String friendsCall = '/friends-call';
  // static const String bubbleCall = '/bubble-call';

  /// Get all authentication routes
  static List<String> get authRoutes => [
    login,
    multiStepSignup,
    forgotPassword,
    resetPasswordVerification,
    createNewPassword,
    resetPassword,
  ];

  /// Get all main app routes
  static List<String> get mainAppRoutes => [
    friends,
    profile,
    userProfile,
    usernameProfile,
    bubble,
    contacts,
  ];

  /// Check if a route is an authentication route
  static bool isAuthRoute(String route) => authRoutes.contains(route);

  /// Check if a route is a main app route
  static bool isMainAppRoute(String route) {
    return mainAppRoutes.contains(route);
  }

  /// Get the default route for authenticated users
  static String get defaultAuthenticatedRoute => bubble;

  /// Get the default route for unauthenticated users
  static String get defaultUnauthenticatedRoute => login;
}

// Root navigators
final _rootNavigatorKey = GlobalKey<NavigatorState>();
final _shellNavigatorKey = GlobalKey<NavigatorState>();

// AuthBloc change notifier for router refresh
class AuthBlocChangeNotifier extends ChangeNotifier {
  StreamSubscription<AuthState>? _authSubscription;

  AuthBlocChangeNotifier() {
    // Get the AuthBloc from the service locator
    final authBloc = di.sl<AuthBloc>();

    // Listen to AuthBloc state changes
    _authSubscription = authBloc.stream.listen((state) {
      // Notify router to refresh when auth state changes
      notifyListeners();
    });
  }

  @override
  void dispose() {
    _authSubscription?.cancel();
    super.dispose();
  }
}

// Create router factory
GoRouter createRouter() {
  return GoRouter(
    initialLocation: AppRoutes.login,
    navigatorKey: _rootNavigatorKey,
    debugLogDiagnostics: true,

    redirect: (context, state) {
      // Always redirect root to login
      if (state.uri.path == '/') {
        return AppRoutes.login;
      }
      return null;
    },
    routes: [
      // Login route
      GoRoute(
        path: AppRoutes.login,
        pageBuilder: (context, state) => NoTransitionPage(
          key: state.pageKey,
          child: BlocProvider<HomeNavigationBloc>(
            create: (_) => sl<HomeNavigationBloc>(),
            child: const LoginPage(),
          ),
        ),
      ),

      // Multi-step Signup route
      GoRoute(
        path: AppRoutes.multiStepSignup,
        pageBuilder: (context, state) => NoTransitionPage(
          key: state.pageKey,
          child: const MultiStepSignupPage(),
        ),
      ),

      // Forgot Password route
      GoRoute(
        path: AppRoutes.forgotPassword,
        pageBuilder: (context, state) => NoTransitionPage(
          key: state.pageKey,
          child: const ForgotPasswordPage(),
        ),
      ),

      // Reset Password Verification route
      GoRoute(
        path: AppRoutes.resetPasswordVerification,
        pageBuilder: (context, state) => NoTransitionPage(
          key: state.pageKey,
          child: const ResetPasswordVerificationPage(),
        ),
      ),

      // Create New Password route
      GoRoute(
        path: AppRoutes.createNewPassword,
        pageBuilder: (context, state) => NoTransitionPage(
          key: state.pageKey,
          child: const CreateNewPasswordPage(),
        ),
      ),

      // Onboarding route
      GoRoute(
        path: AppRoutes.onboarding,
        pageBuilder: (context, state) => NoTransitionPage(
          key: state.pageKey,
          child: BlocBuilder<AuthBloc, AuthState>(
            builder: (context, authState) => OnboardingPage(
              pages: OnboardingData.getPages(),
              onComplete: () {
                context.read<AuthBloc>().add(
                  const MarkOnboardingCompleteEvent(),
                );
                context.go(AppRoutes.bubble);
              },
            ),
          ),
        ),
      ),

      // Main app shell with navigation
      ShellRoute(
        navigatorKey: _shellNavigatorKey,
        builder: (context, state, child) {
          if (kDebugMode) {
            print('Building shell route with: ${state.uri.path}');
          }
          return MainNavigationPageShell(child: child);
        },
        routes: [
          // Profile route
          GoRoute(
            path: AppRoutes.profile,
            pageBuilder: (context, state) => CustomTransitionPage(
              key: state.pageKey,
              child: const ProfilePage(),
              transitionsBuilder: (context, animation, secondaryAnimation, child) {
                return FadeTransition(
                  opacity: animation,
                  child: child,
                );
              },
              transitionDuration: const Duration(milliseconds: 150),
              reverseTransitionDuration: const Duration(milliseconds: 150),
            ),
          ),

          // Friends route
          GoRoute(
            path: AppRoutes.friends,
            pageBuilder: (context, state) => CustomTransitionPage(
              key: state.pageKey,
              child: const FriendsPage(),
              transitionsBuilder: (context, animation, secondaryAnimation, child) {
                return FadeTransition(
                  opacity: animation,
                  child: child,
                );
              },
              transitionDuration: const Duration(milliseconds: 150),
              reverseTransitionDuration: const Duration(milliseconds: 150),
            ),
          ),

          // Bubble route
          GoRoute(
            path: AppRoutes.bubble,
            pageBuilder: (context, state) => CustomTransitionPage(
              key: state.pageKey,
              child: const BubblePage(),
              transitionsBuilder: (context, animation, secondaryAnimation, child) {
                return FadeTransition(
                  opacity: animation,
                  child: child,
                );
              },
              transitionDuration: const Duration(milliseconds: 150),
              reverseTransitionDuration: const Duration(milliseconds: 150),
            ),
          ),

          // Contacts route
          GoRoute(
            path: AppRoutes.contacts,
            pageBuilder: (context, state) => CustomTransitionPage(
              key: state.pageKey,
              child: const ContactsPage(),
              transitionsBuilder: (context, animation, secondaryAnimation, child) {
                return FadeTransition(
                  opacity: animation,
                  child: child,
                );
              },
              transitionDuration: const Duration(milliseconds: 150),
              reverseTransitionDuration: const Duration(milliseconds: 150),
            ),
          ),

          // User Profile route
          GoRoute(
            path: '${AppRoutes.userProfile}/:userId',
            pageBuilder: (context, state) {
              final userId = state.pathParameters['userId'] ?? 'missing_user_id';
              return NoTransitionPage(
                key: state.pageKey,
                child: BlocProvider<UnifiedProfileBloc>(
                  create: (_) => sl<UnifiedProfileBloc>(),
                  child: UnifiedProfilePage(
                    userId: userId,
                  ),
                ),
              );
            },
          ),
        ],
      ),

      // Incoming Friend Call route
      GoRoute(
        path: IncomingFriendCallPage.routeName,
        pageBuilder: (context, state) {
          final args = state.extra as Map<String, dynamic>? ?? {};
          return NoTransitionPage(
            key: state.pageKey,
            child: IncomingFriendCallPage(
              callId: args['callId'] as String? ?? 'missing_call_id',
              callerName: args['callerName'] as String? ?? 'Unknown Caller',
              callerAvatarUrl: args['callerAvatarUrl'] as String?,
              isVideoOffered: args['isVideoOffered'] as bool? ?? false,
              isAudioOffered: args['isAudioOffered'] as bool? ?? false,
              isScreenShareOffered: args['isScreenShareOffered'] as bool? ?? false,
            ),
          );
        },
      ),

      // Incoming Bubble Call route
      GoRoute(
        path: IncomingBubbleCallPage.routeName,
        pageBuilder: (context, state) {
          final args = state.extra as Map<String, dynamic>? ?? {};
          return NoTransitionPage(
            key: state.pageKey,
            child: IncomingBubbleCallPage(
              callId: args['callId'] as String? ?? 'missing_call_id',
              bubbleName: args['bubbleName'] as String? ?? 'Unknown Bubble',
              bubbleAvatarUrl: args['bubbleAvatarUrl'] as String?,
              isVideoOffered: args['isVideoOffered'] as bool? ?? false,
              isAudioOffered: args['isAudioOffered'] as bool? ?? false,
              isScreenShareOffered: args['isScreenShareOffered'] as bool? ?? false,
            ),
          );
        },
      ),

      // Friends Call route
      GoRoute(
        path: FriendsCallPage.routeName,
        pageBuilder: (context, state) {
          final args = state.extra as Map<String, dynamic>? ?? {};
          return NoTransitionPage(
            key: state.pageKey,
            child: FriendsCallPage(
              friendId: args['friendId'] as String? ?? 'missing_friend_id',
              friendName: args['friendName'] as String? ?? 'Unknown Friend',
              friendProfilePhoto: args['friendProfilePhoto'] as String?,
              initialIsVideoEnabled: args['initialIsVideoEnabled'] as bool? ?? false,
              initialIsScreenSharing: args['initialIsScreenSharing'] as bool? ?? false,
            ),
          );
        },
      ),

      // Username-based profile route (outside shell for public access)
      GoRoute(
        path: '${AppRoutes.usernameProfile}/:username',
        pageBuilder: (context, state) {
          final username = state.pathParameters['username'] ?? 'missing_username';
          return NoTransitionPage(
            key: state.pageKey,
            child: BlocProvider<UnifiedProfileBloc>(
              create: (_) => sl<UnifiedProfileBloc>(),
              child: UsernameProfilePage(
                username: username,
              ),
            ),
          );
        },
      ),

      // Bubble Call route
      GoRoute(
        path: BubbleCallPage.routeName,
        pageBuilder: (context, state) {
          final args = state.extra as Map<String, dynamic>? ?? {};
          return NoTransitionPage(
            key: state.pageKey,
            child: BubbleCallPage(
              bubbleId: args['bubbleId'] as String? ?? 'missing_bubble_id',
              initialIsVideoEnabled: args['initialIsVideoEnabled'] as bool? ?? false,
              initialIsScreenSharing: args['initialIsScreenSharing'] as bool? ?? false,
            ),
          );
        },
      ),
    ],
    errorBuilder: (context, state) {
      if (kDebugMode) {
        print('Navigation error: ${state.error}');
      }
      return const LoginPage();
    },
  );
}

class MainNavigationPageShell extends StatelessWidget {
  final Widget child;

  const MainNavigationPageShell({required this.child, super.key});

  @override
  Widget build(BuildContext context) {
    final navBarVisibilityNotifier = Provider.of<NavBarVisibilityNotifier>(
      context,
    );
    final isNavBarVisible = navBarVisibilityNotifier.isNavBarVisible;
    const animationDuration = Duration(milliseconds: 400);

    final bottomPadding = MediaQuery.of(context).padding.bottom;

    return Material(
      type: MaterialType.transparency,
      child: Stack(
        fit: StackFit.expand,
        children: [
          Positioned.fill(child: child),

          // Gradient background behind navigation bar
          AnimatedPositioned(
            duration: animationDuration,
            curve: Curves.easeInOut,
            left: 0,
            right: 0,
            bottom: isNavBarVisible ? 0 : -100 - bottomPadding,
            height: 48 + bottomPadding,
            child: Container(
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  begin: Alignment.topCenter,
                  end: Alignment.bottomCenter,
                  colors: [
                    Colors.black.withOpacity(0),
                    Colors.black.withOpacity(0.9),
                  ],
                ),
              ),
            ),
          ),

          // Navigation bar
          AnimatedPositioned(
            duration: animationDuration,
            curve: Curves.easeInOut,
            left: 0,
            right: 0,
            bottom: isNavBarVisible ? -5 : -100,
            child: const MainNavigationBar(),
          ),
        ],
      ),
    );
  }
}

class MainNavigationBar extends StatefulWidget {
  const MainNavigationBar({super.key});

  @override
  State<MainNavigationBar> createState() => _MainNavigationBarState();
}

class _MainNavigationBarState extends State<MainNavigationBar> with TickerProviderStateMixin {
  late AnimationController _animationController;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: const Duration(seconds: 2),
      vsync: this,
    );
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  void _showFeatureNotAvailableMessage(BuildContext context, String feature) {
    BubbleCallDialog.show(context, [], '');
  }

  @override
  Widget build(BuildContext context) {
    final currentLocation = GoRouterState.of(context).uri.path;

    return SafeArea(
      top: false,
      left: false,
      right: false,
      maintainBottomViewPadding: true,
      child: Container(
        height: 48,
        padding: EdgeInsets.zero,
        color: Colors.transparent,
        child: Row(
          mainAxisAlignment: MainAxisAlignment.spaceAround,
          children: [
            _buildNavButton(
              AppRoutes.profile,
              'assets/navbar/profile.svg',
              currentLocation,
            ),
            _buildNavButton(
              AppRoutes.friends,
              'assets/navbar/friends.svg',
              currentLocation,
            ),
            _buildCenterLogo(context),
            _buildNavButton(
              AppRoutes.bubble,
              'assets/navbar/bubble.svg',
              currentLocation,
            ),
            _buildNavButton(
              AppRoutes.contacts,
              'assets/navbar/contacts.svg',
              currentLocation,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildNavButton(
    String routePath,
    String iconPath,
    String currentPath,
  ) {
    final isSelected = currentPath == routePath;
    final navBarVisibilityNotifier = Provider.of<NavBarVisibilityNotifier>(
      context,
    );
    final isNavBarVisible = navBarVisibilityNotifier.isNavBarVisible;

    return Expanded(
      child: GestureDetector(
        onTap: () {
          if (!isSelected) {
            context.go(routePath);
          }
        },
        behavior: HitTestBehavior.translucent,
        child: DecoratedBox(
          decoration: isSelected && isNavBarVisible
              ? BoxDecoration(
                  boxShadow: [
                    BoxShadow(
                      color: const Color(0xFF00FFFF).withOpacity(0.5),
                      blurRadius: 40,
                      spreadRadius: 1,
                      offset: const Offset(0, 2),
                    ),
                  ],
                )
              : const BoxDecoration(),
          child: SvgPicture.asset(
            iconPath,
            width: 28,
            height: 28,
            colorFilter: ColorFilter.mode(
              isSelected ? const Color(0xFF00FFFF) : Colors.white,
              BlendMode.srcIn,
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildCenterLogo(BuildContext context) {
    print('DEBUG: _buildCenterLogo called in CustomBottomNavBar');
    return Expanded(
      child: Center(
        child: SizedBox(
          width: 48,
          height: 48,
          child: BlocListener<CallBloc, CallState>(
            listener: (context, callState) {
              final bubbleState = context.read<BubbleBloc>().state;
              print('DEBUG: BlocListener - BubbleState: ${bubbleState.runtimeType}, CallState: ${callState.runtimeType}');

              // Start animation when in bubble and call is active
              if (bubbleState is BubbleLoaded &&
                  bubbleState.bubble.id.value.isNotEmpty &&
                  callState.isCallActive &&
                  callState.activeCallGroupId == bubbleState.bubble.id.value) {
                if (!_animationController.isAnimating) {
                  _animationController.repeat();
                }
              } else {
                // Stop animation when not in active call or not in bubble
                if (_animationController.isAnimating) {
                  _animationController.stop();
                  _animationController.reset();
                }
              }
            },
            child: BlocBuilder<BubbleBloc, BubbleState>(
              builder: (context, bubbleState) {
                print('DEBUG: CustomBottomNavBar BubbleBloc builder - BubbleState: ${bubbleState.runtimeType}');
                return BlocBuilder<CallBloc, CallState>(
                  builder: (context, callState) {
                    print('DEBUG: CustomBottomNavBar CallBloc builder - CallState: ${callState.runtimeType}');
                    return GestureDetector(
                      onTap: () => _handleCenterButtonTap(context, bubbleState, callState),
                      behavior: HitTestBehavior.translucent,
                      child: _buildButtonContent(context, bubbleState, callState),
                    );
                  },
                );
              },
            ),
          ),
        ),
      ),
    );
  }

  void _handleCenterButtonTap(BuildContext context, BubbleState bubbleState, CallState callState) {
    print('DEBUG: Center button tapped in CustomBottomNavBar');

    // User is considered in-bubble only if BubbleLoaded and bubble id is not empty
    final userInBubble = bubbleState is BubbleLoaded &&
        bubbleState.bubble.id.value.isNotEmpty;

    // Case 1: User not in a bubble (BubbleInitial or empty bubble) - show demo dialog
    if (bubbleState is BubbleInitial || !userInBubble) {
      print('DEBUG: Showing demo dialog - user not in bubble');
      HopenButtonDemoDialog.show(context);
      return;
    }

    // Case 2: User in bubble but no active call - start call
    if (bubbleState is BubbleLoaded && !callState.isCallActive) {
      print('DEBUG: Starting call - user in bubble, no active call');
      final bubble = bubbleState.bubble;
      // Convert BubbleMemberEntity to BubbleMember
      final members = bubble.members.map((memberEntity) => BubbleMember(
        id: memberEntity.id.value,
        bubbleId: bubble.id.value,
        userId: memberEntity.id.value,
        name: memberEntity.name,
        profilePicUrl: memberEntity.avatarUrl,
        joinedAt: memberEntity.joinedAt,
      )).toList();

      BubbleCallDialog.show(
        context,
        members,
        bubble.id.value,
        currentBubbleName: bubble.name.value,
      );
      return;
    }

    // Case 3: User in bubble with active call - join call
    if (bubbleState is BubbleLoaded && callState.isCallActive) {
      print('DEBUG: Joining call - user in bubble with active call');
      final bubble = bubbleState.bubble;
      if (callState.activeCallGroupId == bubble.id.value) {
        // Convert BubbleMemberEntity to BubbleMember
        final members = bubble.members.map((memberEntity) => BubbleMember(
          id: memberEntity.id.value,
          bubbleId: bubble.id.value,
          userId: memberEntity.id.value,
          name: memberEntity.name,
          profilePicUrl: memberEntity.avatarUrl,
          joinedAt: memberEntity.joinedAt,
        )).toList();

        // Join the active call for this bubble
        BubbleCallDialog.show(
          context,
          members,
          bubble.id.value,
          isJoining: true,
          currentBubbleName: bubble.name.value,
        );
      } else {
        // Different bubble has active call - show demo dialog
        print('DEBUG: Different bubble has active call - showing demo dialog');
        HopenButtonDemoDialog.show(context);
      }
    }
  }

  Widget _buildButtonContent(BuildContext context, BubbleState bubbleState, CallState callState) {
    print('DEBUG: Building button content - BubbleState: ${bubbleState.runtimeType}, CallState: ${callState.runtimeType}');

    // Case 1: User NOT in a bubble - WHITE button (deactivated)
    if (bubbleState is BubbleInitial) {
      print('DEBUG: Showing WHITE button (BubbleInitial)');
      return SvgPicture.asset(
        'assets/icons/hopen-logo.svg',
        width: 48,
        height: 48,
        colorFilter: const ColorFilter.mode(Colors.white, BlendMode.srcIn),
      );
    }

    // Case 2 & 3: User IS in a bubble
    if (bubbleState is BubbleLoaded) {
      final bubble = bubbleState.bubble;

      // Check if bubble has valid ID and members (user is actually in a bubble)
      final userActuallyInBubble = bubble.id.value.isNotEmpty && bubble.members.isNotEmpty;

      if (!userActuallyInBubble) {
        print('DEBUG: Showing WHITE button (BubbleLoaded but no valid bubble)');
        return SvgPicture.asset(
          'assets/icons/hopen-logo.svg',
          width: 48,
          height: 48,
          colorFilter: const ColorFilter.mode(Colors.white, BlendMode.srcIn),
        );
      }

      // User IS in a bubble - check if there's an active call
      final hasActiveCall = callState.isCallActive && callState.activeCallGroupId == bubble.id.value;

      if (hasActiveCall) {
        // Case 3: ANIMATED MULTICOLOR GRADIENT button (in bubble + active call)
        print('DEBUG: Showing ANIMATED GRADIENT button (in bubble + active call)');

        const gradientColors = <Color>[
          Color(0xFFFF00FF),
          Color(0xFFFB43BB),
          Color(0xFFF3C935),
          Color(0xFFF0FF00),
          Color(0xFFC4FF2D),
          Color(0xFF91FF64),
          Color(0xFF64FF93),
          Color(0xFF40FFBA),
          Color(0xFF24FFD8),
          Color(0xFF10FFED),
          Color(0xFF04FFFA),
          Color(0xFF00FFFF),
        ];

        const gradientStops = <double>[
          0,
          0.12,
          0.37,
          0.48,
          0.53,
          0.6,
          0.67,
          0.74,
          0.81,
          0.88,
          0.94,
          1,
        ];

        return AnimatedBuilder(
          animation: _animationController,
          builder: (context, child) => ShaderMask(
            shaderCallback: (bounds) => LinearGradient(
              colors: gradientColors,
              stops: gradientStops,
              begin: Alignment.centerLeft,
              end: Alignment.centerRight,
              transform: GradientRotation(_animationController.value * 2 * math.pi),
            ).createShader(bounds),
            blendMode: BlendMode.srcIn,
            child: SvgPicture.asset(
              'assets/icons/hopen-logo.svg',
              width: 48,
              height: 48,
              fit: BoxFit.fill,
            ),
          ),
        );
      } else {
        // Case 2: STATIC MULTICOLOR GRADIENT button (in bubble, no active call)
        print('DEBUG: Showing STATIC GRADIENT button (in bubble, no active call)');

        const gradientColors = <Color>[
          Color(0xFFFF00FF),
          Color(0xFFFB43BB),
          Color(0xFFF3C935),
          Color(0xFFF0FF00),
          Color(0xFFC4FF2D),
          Color(0xFF91FF64),
          Color(0xFF64FF93),
          Color(0xFF40FFBA),
          Color(0xFF24FFD8),
          Color(0xFF10FFED),
          Color(0xFF04FFFA),
          Color(0xFF00FFFF),
        ];

        const gradientStops = <double>[
          0,
          0.12,
          0.37,
          0.48,
          0.53,
          0.6,
          0.67,
          0.74,
          0.81,
          0.88,
          0.94,
          1,
        ];

        return ShaderMask(
          shaderCallback: (bounds) => const LinearGradient(
            colors: gradientColors,
            stops: gradientStops,
            begin: Alignment.centerLeft,
            end: Alignment.centerRight,
          ).createShader(bounds),
          blendMode: BlendMode.srcIn,
          child: SvgPicture.asset(
            'assets/icons/hopen-logo.svg',
            width: 48,
            height: 48,
            fit: BoxFit.fill,
          ),
        );
      }
    }

    // Fallback: WHITE button
    print('DEBUG: Fallback - showing WHITE button');
    return SvgPicture.asset(
      'assets/icons/hopen-logo.svg',
      width: 48,
      height: 48,
      colorFilter: const ColorFilter.mode(Colors.white, BlendMode.srcIn),
    );
  }
}

