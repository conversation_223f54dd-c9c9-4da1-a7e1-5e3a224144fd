import '../../../repositories/auth/auth_repository.dart';
import '../../../repositories/bubble/bubble_repository.dart';
import '../error/result.dart';

import '../error/failures.dart';
import '../error/base_error.dart';
import '../models/bubble_entity.dart';
import '../models/value_objects.dart';

/// Base use case interface
abstract class UseCase<TResult, TParams> {
  Future<Result<TResult>> execute(TParams params);
}

/// Enhanced base use case with common functionality
abstract class EnhancedUseCase<TResult, TParams> extends UseCase<TResult, TParams> {
  /// Execute with automatic error handling and logging
  @override
  Future<Result<TResult>> execute(TParams params) async {
    return ResultUtils.tryCallAsync(() => executeImpl(params))
      .tapAsync((result) async => onSuccess(result))
      .tapErrorAsync((error) async => onError(error));
  }

  /// Implementation to be provided by concrete use cases
  Future<TResult> executeImpl(TParams params);

  /// Called on successful execution (for logging, analytics, etc.)
  Future<void> onSuccess(TResult result) async {}

  /// Called on error (for logging, error reporting, etc.)
  Future<void> onError(BaseError error) async {}

  /// Validate parameters before execution
  Result<TParams> validateParams(TParams params) => Result.success(params);

  /// Execute with parameter validation
  Future<Result<TResult>> executeWithValidation(TParams params) async {
    try {
      final validationResult = validateParams(params);
      if (validationResult.isFailure) {
        return Result.failure(validationResult.error!);
      }
      return await execute(validationResult.data!);
    } catch (e) {
      return Result.failure(UnexpectedFailure(message: e.toString()));
    }
  }
}

/// Parameters for loading user's bubble
class LoadUserBubbleParams {
  
  const LoadUserBubbleParams({this.bubbleId});
  final String? bubbleId;
}

/// Use case for loading user's current bubble
class LoadUserBubbleUseCase implements UseCase<BubbleEntity?, LoadUserBubbleParams> {

  const LoadUserBubbleUseCase(this._bubbleRepository, this._authRepository);
  final BubbleRepository _bubbleRepository;
  final AuthRepository _authRepository;

  @override
  Future<Result<BubbleEntity?>> execute(LoadUserBubbleParams params) async {
    try {
      // Get current user
      final currentUserResult = await _authRepository.getCurrentUser();
      if (currentUserResult.isFailure) {
        return Result.failure(const AuthenticationError(message: 'User not authenticated'));
      }
      
      final currentUser = currentUserResult.data;

      // If no specific bubble ID, get user's active bubble
      if (params.bubbleId == null) {
        final userBubblesResult = await _bubbleRepository.getBubbles(UserId(currentUser.id));
        if (userBubblesResult.isFailure) {
          return Result.failure(userBubblesResult.error);
        }
        
        final userBubbles = userBubblesResult.data;
        if (userBubbles.isEmpty) {
          return Result.success(null); // User not in any bubble
        }

        // Load the first active bubble
        final activeBubbleInfo = userBubbles.first;
        final bubbleResult = await _bubbleRepository.getBubbleDetailsById(activeBubbleInfo.id);
        if (bubbleResult.isFailure) {
          return Result.failure(bubbleResult.error);
        }
        
        return Result.success(_convertToEntity(bubbleResult.data));
      }

      // Load specific bubble
      final bubbleResult = await _bubbleRepository.getBubbleDetailsById(BubbleId(params.bubbleId!));
      if (bubbleResult.isFailure) {
        return Result.failure(bubbleResult.error);
      }
      
      return Result.success(_convertToEntity(bubbleResult.data));
    } catch (e, stackTrace) {
      return Result.failure(UnexpectedFailure(
        message: e.toString(),
      ));
    }
  }

  BubbleEntity _convertToEntity(BubbleEntity bubble) {
    // The repository already returns a BubbleEntity, so no conversion needed
    return bubble;
  }
}

/// Parameters for creating a bubble
class CreateBubbleParams {

  const CreateBubbleParams({
    required this.name,
    this.description,
    this.capacity,
    this.endDate,
    this.invitedMemberIds = const [],
  });
  final String name;
  final String? description;
  final int? capacity;
  final DateTime? endDate;
  final List<String> invitedMemberIds;
}

/// Use case for creating a new bubble with enhanced validation
class CreateBubbleUseCase implements UseCase<BubbleEntity, CreateBubbleParams> {

  const CreateBubbleUseCase(this._bubbleRepository, this._authRepository);
  final BubbleRepository _bubbleRepository;
  final AuthRepository _authRepository;

  @override
  Future<Result<BubbleEntity>> execute(CreateBubbleParams params) async {
    try {
      // Get current user
      final currentUserResult = await _authRepository.getCurrentUser();
      if (currentUserResult.isFailure) {
        return Result.failure(const AuthenticationError(message: 'User must be authenticated to create a bubble'));
      }
      
  // final currentUser = currentUserResult.data;

      // Validate bubble name
      final nameResult = BubbleName.create(params.name);
      if (nameResult.isFailure) {
        return Result.failure(nameResult.error);
      }

      // Validate capacity if provided
      if (params.capacity != null) {
        final capacityResult = MemberCapacity.create(params.capacity!);
        if (capacityResult.isFailure) {
          return Result.failure(capacityResult.error);
        }
        // Note: capacity validation is done but not currently used in bubble creation
      }

      // Create the bubble
      final bubbleResult = await _bubbleRepository.createBubble(
        name: nameResult.data,
        description: '', // Bubbles don't have descriptions
      );
      
      if (bubbleResult.isFailure) {
        return Result.failure(bubbleResult.error);
      }
      
      return Result.success(bubbleResult.data);
    } catch (e, stackTrace) {
      return Result.failure(UnexpectedFailure(
        message: e.toString(),
      ));
    }
  }
}

/// Parameters for joining a bubble
class JoinBubbleParams {
  
  const JoinBubbleParams({required this.bubbleId});
  final String bubbleId;
}

/// Use case for joining an existing bubble
class JoinBubbleUseCase implements UseCase<void, JoinBubbleParams> {

  const JoinBubbleUseCase(this._bubbleRepository, this._authRepository);
  final BubbleRepository _bubbleRepository;
  final AuthRepository _authRepository;

  @override
  Future<Result<void>> execute(JoinBubbleParams params) async {
    try {
      // Get current user
      final currentUserResult = await _authRepository.getCurrentUser();
      if (currentUserResult.isFailure) {
        return Result.failure(const AuthenticationError(message: 'User must be authenticated to join a bubble'));
      }
      
      final currentUser = currentUserResult.data;

      // Validate bubble ID
      final bubbleIdResult = BubbleId.create(params.bubbleId);
      if (bubbleIdResult.isFailure) {
        return Result.failure(bubbleIdResult.error);
      }

      // Check if user is already in a bubble
      final userBubblesResult = await _bubbleRepository.getBubbles(UserId(currentUser.id));
      if (userBubblesResult.isFailure) {
        return Result.failure(userBubblesResult.error);
      }
      
      final userBubbles = userBubblesResult.data;
      if (userBubbles.isNotEmpty) {
        return Result.failure(ValidationError(
          message: 'User already in a bubble: ${userBubbles.first.id.value}',
        ),);
      }

      // Get bubble details to validate join operation
      final bubbleResult = await _bubbleRepository.getBubbleDetailsById(bubbleIdResult.data);
      if (bubbleResult.isFailure) {
        return Result.failure(bubbleResult.error);
      }

      // Join the bubble
      final joinResult = await _bubbleRepository.joinBubble(
        bubbleId: bubbleIdResult.data,
        userId: UserId(currentUser.id),
      );
      
      if (joinResult.isFailure) {
        return Result.failure(joinResult.error);
      }
      
      return Result.success(null);
    } catch (e, stackTrace) {
      return Result.failure(UnexpectedFailure(
        message: e.toString(),
      ));
    }
  }
}

/// Parameters for leaving a bubble
class LeaveBubbleParams {
  
  const LeaveBubbleParams({required this.bubbleId});
  final String bubbleId;
}

/// Use case for leaving a bubble
class LeaveBubbleUseCase implements UseCase<void, LeaveBubbleParams> {

  const LeaveBubbleUseCase(this._bubbleRepository, this._authRepository);
  final BubbleRepository _bubbleRepository;
  final AuthRepository _authRepository;

  @override
  Future<Result<void>> execute(LeaveBubbleParams params) async {
    try {
      // Get current user
      final currentUserResult = await _authRepository.getCurrentUser();
      if (currentUserResult.isFailure) {
        return Result.failure(const AuthenticationError(message: 'User must be authenticated to leave a bubble'));
      }
      
      final currentUser = currentUserResult.data;

      // Validate bubble ID
      final bubbleIdResult = BubbleId.create(params.bubbleId);
      if (bubbleIdResult.isFailure) {
        return Result.failure(bubbleIdResult.error);
      }

      // Get bubble details to validate leave operation
      final bubbleResult = await _bubbleRepository.getBubbleDetailsById(bubbleIdResult.data);
      if (bubbleResult.isFailure) {
        return Result.failure(bubbleResult.error);
      }

      // Leave the bubble
      final leaveResult = await _bubbleRepository.leaveBubble(
        bubbleId: bubbleIdResult.data,
        userId: UserId(currentUser.id),
      );
      
      if (leaveResult.isFailure) {
        return Result.failure(leaveResult.error);
      }
      
      return Result.success(null);
    } catch (e, stackTrace) {
      return Result.failure(UnexpectedFailure(
        message: e.toString(),
      ));
    }
  }
}

/// Parameters for inviting users to a bubble
class InviteToBubbleParams {
  
  const InviteToBubbleParams({
    required this.bubbleId,
    required this.userIds,
  });
  final String bubbleId;
  final List<String> userIds;
}

/// Use case for inviting users to a bubble
class InviteToBubbleUseCase implements UseCase<void, InviteToBubbleParams> {

  const InviteToBubbleUseCase(this._bubbleRepository, this._authRepository);
  final BubbleRepository _bubbleRepository;
  final AuthRepository _authRepository;

  @override
  Future<Result<void>> execute(InviteToBubbleParams params) async {
    try {
      // Get current user
      final currentUserResult = await _authRepository.getCurrentUser();
      if (currentUserResult.isFailure) {
        return Result.failure(const AuthenticationError(message: 'User must be authenticated to invite users'));
      }
      
  // final currentUser = currentUserResult.data;

      // Validate bubble ID
      final bubbleIdResult = BubbleId.create(params.bubbleId);
      if (bubbleIdResult.isFailure) {
        return Result.failure(bubbleIdResult.error);
      }

      // Get bubble details to validate invite operation
      final bubbleResult = await _bubbleRepository.getBubbleDetailsById(bubbleIdResult.data);
      if (bubbleResult.isFailure) {
        return Result.failure(bubbleResult.error);
      }

      // Send invites to each user
      final inviteResult = await _bubbleRepository.inviteToBubble(
        bubbleId: bubbleIdResult.data,
        inviterId: UserId(''), // TODO: Get from current user
        inviteeIds: params.userIds.map(UserId.new).toList(),
      );

      if (inviteResult.isFailure) {
        return Result.failure(inviteResult.error);
      }

      return Result.success(null);
    } catch (e, stackTrace) {
      return Result.failure(UnexpectedFailure(
        message: e.toString(),
      ));
    }
  }
}

/// Parameters for inviting a single user to a bubble (for backward compatibility)
class InviteUserToBubbleParams {
  
  const InviteUserToBubbleParams({
    required this.bubbleId,
    required this.userId,
  });
  final String bubbleId;
  final String userId;
}

/// Use case for inviting a single user to a bubble (for backward compatibility)
class InviteUserToBubbleUseCase implements UseCase<void, InviteUserToBubbleParams> {

  const InviteUserToBubbleUseCase(this._bubbleRepository, this._authRepository);
  final BubbleRepository _bubbleRepository;
  final AuthRepository _authRepository;

  @override
  Future<Result<void>> execute(InviteUserToBubbleParams params) async {
    // Delegate to the multi-user invite use case
    final multiInviteParams = InviteToBubbleParams(
      bubbleId: params.bubbleId,
      userIds: [params.userId],
    );
    
    final inviteUseCase = InviteToBubbleUseCase(_bubbleRepository, _authRepository);
    return inviteUseCase.execute(multiInviteParams);
  }
}

/// Parameters for updating member status
class UpdateMemberStatusParams {
  
  const UpdateMemberStatusParams({
    required this.bubbleId,
    required this.memberId,
    this.isOnline,
    this.unreadMessageCount,
  });
  final String bubbleId;
  final String memberId;
  final bool? isOnline;
  final int? unreadMessageCount;
}

/// Use case for updating member status in a bubble
class UpdateMemberStatusUseCase implements UseCase<BubbleEntity, UpdateMemberStatusParams> {

  const UpdateMemberStatusUseCase(this._bubbleRepository);
  final BubbleRepository _bubbleRepository;

  @override
  Future<Result<BubbleEntity>> execute(UpdateMemberStatusParams params) async {
    try {
      // Validate parameters
      final bubbleIdResult = BubbleId.create(params.bubbleId);
      if (bubbleIdResult.isFailure) {
        return Result.failure(bubbleIdResult.error);
      }

      final memberIdResult = UserId.create(params.memberId);
      if (memberIdResult.isFailure) {
        return Result.failure(memberIdResult.error);
      }

      // Validate unread count if provided
      if (params.unreadMessageCount != null && params.unreadMessageCount! < 0) {
        return Result.failure(const ValidationError(message: 'Unread message count cannot be negative'));
      }

      // Update member status and return the updated bubble
      return await _bubbleRepository.updateMemberStatus(
        bubbleId: bubbleIdResult.data,
        memberId: memberIdResult.data,
        isOnline: params.isOnline,
        unreadMessageCount: params.unreadMessageCount,
      );
    } catch (e, stackTrace) {
      return Result.failure(UnexpectedFailure(
        message: e.toString(),
      ));
    }
  }
}

/// Parameters for getting bubble statistics
class GetBubbleStatsParams {
  
  const GetBubbleStatsParams({required this.bubbleId});
  final String bubbleId;
}

/// Use case for getting bubble statistics
class GetBubbleStatsUseCase implements UseCase<Map<String, dynamic>, GetBubbleStatsParams> {

  const GetBubbleStatsUseCase(this._bubbleRepository, this._authRepository);
  final BubbleRepository _bubbleRepository;
  final AuthRepository _authRepository;

  @override
  Future<Result<Map<String, dynamic>>> execute(GetBubbleStatsParams params) async {
    try {
      // Get current user
      final currentUserResult = await _authRepository.getCurrentUser();
      if (currentUserResult.isFailure) {
        return Result.failure(const AuthenticationError(message: 'User must be authenticated'));
      }

      // Validate bubble ID
      final bubbleIdResult = BubbleId.create(params.bubbleId);
      if (bubbleIdResult.isFailure) {
        return Result.failure(bubbleIdResult.error);
      }

      // Get bubble statistics
      final bubbleResult = await _bubbleRepository.getBubbleDetailsById(bubbleIdResult.data);
      if (bubbleResult.isFailure) {
        return Result.failure(bubbleResult.error);
      }
      
      // Extract statistics from bubble data
  // final bubble = bubbleResult.data;
      final stats = {
        'memberCount': 0, // Would be extracted from actual bubble data
        'messageCount': 0, // Would be extracted from actual bubble data
        'activeMembers': 0, // Would be extracted from actual bubble data
      };
      
      return Result.success(stats);
    } catch (e, stackTrace) {
      return Result.failure(UnexpectedFailure(
        message: e.toString(),
      ));
    }
  }
} 