import 'package:equatable/equatable.dart';

// Enums
enum BubbleRequestType {
  inviteToOwnBubble,
  requestToJoinBubble,
  startBubbleTogether,
}

enum ProfilePageActionType {
  sendContactRequest,
  acceptContactRequest,
  declineContactRequest,
  sendMessage,
  startBubbleTogether,
  unblockUser,
  unfriendUser,
  blockUser,
  muteUser,
  reportUser,
}

// Events
abstract class UnifiedProfileEvent extends Equatable {
  const UnifiedProfileEvent();

  @override
  List<Object?> get props => [];
}

class LoadUnifiedProfile extends UnifiedProfileEvent {
  const LoadUnifiedProfile({required this.userId});

  final String userId;

  @override
  List<Object?> get props => [userId];
}

class LoadUserProfileByUsernameEvent extends UnifiedProfileEvent {
  const LoadUserProfileByUsernameEvent(this.username);

  final String username;

  @override
  List<Object?> get props => [username];
}

class SendContactRequest extends UnifiedProfileEvent {
  const SendContactRequest(this.targetUserId);
  
  final String targetUserId;

  @override
  List<Object?> get props => [targetUserId];
}

class AcceptContactRequest extends UnifiedProfileEvent {
  const AcceptContactRequest(this.requesterId);
  
  final String requesterId;

  @override
  List<Object?> get props => [requesterId];
}

class DeclineContactRequestEvent extends UnifiedProfileEvent {
  const DeclineContactRequestEvent({required this.requesterId});
  
  final String requesterId;

  @override
  List<Object?> get props => [requesterId];
}

class SendBubbleRequestEvent extends UnifiedProfileEvent {
  const SendBubbleRequestEvent({
    required this.targetUserId,
    required this.requestType,
  });
  
  final String targetUserId;
  final BubbleRequestType requestType;

  @override
  List<Object?> get props => [targetUserId, requestType];
}

// Events for request dialog notifications
class ContactRequestAcceptedEvent extends UnifiedProfileEvent {
  const ContactRequestAcceptedEvent({required this.requesterId});
  
  final String requesterId;

  @override
  List<Object?> get props => [requesterId];
}

class ContactRequestDeclinedEvent extends UnifiedProfileEvent {
  const ContactRequestDeclinedEvent({required this.requesterId});
  
  final String requesterId;

  @override
  List<Object?> get props => [requesterId];
}

class BubbleRequestAcceptedEvent extends UnifiedProfileEvent {
  const BubbleRequestAcceptedEvent({required this.targetUserId});
  
  final String targetUserId;

  @override
  List<Object?> get props => [targetUserId];
}

class BubbleRequestDeclinedEvent extends UnifiedProfileEvent {
  const BubbleRequestDeclinedEvent({required this.targetUserId});
  
  final String targetUserId;

  @override
  List<Object?> get props => [targetUserId];
}

class UnblockUser extends UnifiedProfileEvent {
  const UnblockUser(this.userId);
  
  final String userId;

  @override
  List<Object?> get props => [userId];
}

class UnfriendUserEvent extends UnifiedProfileEvent {
  const UnfriendUserEvent({required this.targetUserId});
  
  final String targetUserId;

  @override
  List<Object?> get props => [targetUserId];
}

class MuteUserEvent extends UnifiedProfileEvent {
  const MuteUserEvent({required this.targetUserId});
  
  final String targetUserId;

  @override
  List<Object?> get props => [targetUserId];
}

class ReportUserEvent extends UnifiedProfileEvent {
  const ReportUserEvent({required this.targetUserId, required this.reason});
  
  final String targetUserId;
  final String reason;

  @override
  List<Object?> get props => [targetUserId, reason];
}

class BlockUserEvent extends UnifiedProfileEvent {
  const BlockUserEvent({required this.targetUserId});
  
  final String targetUserId;

  @override
  List<Object?> get props => [targetUserId];
} 