import 'package:flutter_test/flutter_test.dart';
import 'package:hopen/provider/services/image_processing_service.dart';
import 'package:hopen/provider/repositories/profile_picture/profile_picture_repository_impl.dart';

void main() {
  group('HEIF/HEIC Support Tests', () {
    test('ImageProcessingService should include HEIF and HEIC in allowed extensions', () {
      // Arrange & Act
      const allowedExtensions = ImageProcessingService.allowedExtensions;
      
      // Assert
      expect(allowedExtensions, contains('heif'));
      expect(allowedExtensions, contains('heic'));
      expect(allowedExtensions, contains('jpg'));
      expect(allowedExtensions, contains('jpeg'));
      expect(allowedExtensions, contains('png'));
      expect(allowedExtensions, contains('webp'));
    });

    test('ImageProcessingService should include HEIF and HEIC in allowed MIME types', () {
      // Arrange & Act
      const allowedMimeTypes = ImageProcessingService.allowedMimeTypes;
      
      // Assert
      expect(allowedMimeTypes, contains('image/heif'));
      expect(allowedMimeTypes, contains('image/heic'));
      expect(allowedMimeTypes, contains('image/jpeg'));
      expect(allowedMimeTypes, contains('image/jpg'));
      expect(allowedMimeTypes, contains('image/png'));
      expect(allowedMimeTypes, contains('image/webp'));
    });

    test('ProfilePictureRepositoryImpl should include HEIF and HEIC in allowed formats', () {
      // Arrange & Act
      const allowedFormats = ProfilePictureRepositoryImpl.allowedFormats;
      
      // Assert
      expect(allowedFormats, contains('heif'));
      expect(allowedFormats, contains('heic'));
      expect(allowedFormats, contains('jpg'));
      expect(allowedFormats, contains('jpeg'));
      expect(allowedFormats, contains('png'));
      expect(allowedFormats, contains('webp'));
    });

    // Note: _detectImageFormat is private, so we test it indirectly through format validation
    test('should validate HEIF and HEIC extensions in file paths', () {
      // Test that HEIF/HEIC extensions are recognized as valid
      const heifExtensions = ['.heif', '.heic'];
      const allowedExtensions = ImageProcessingService.allowedExtensions;

      for (final ext in heifExtensions) {
        final cleanExt = ext.startsWith('.') ? ext.substring(1) : ext;
        expect(allowedExtensions, contains(cleanExt),
               reason: 'Extension $cleanExt should be in allowed extensions');
      }
    });

    test('should maintain backward compatibility with existing formats', () {
      // Ensure all previously supported formats are still supported
      const originalFormats = ['jpg', 'jpeg', 'png', 'webp'];
      const currentFormats = ImageProcessingService.allowedExtensions;
      
      for (final format in originalFormats) {
        expect(currentFormats, contains(format), 
               reason: 'Format $format should still be supported for backward compatibility');
      }
    });

    test('should have correct format count after adding HEIF/HEIC', () {
      // Verify we have the expected number of formats
      const allowedExtensions = ImageProcessingService.allowedExtensions;
      const allowedMimeTypes = ImageProcessingService.allowedMimeTypes;
      const allowedFormats = ProfilePictureRepositoryImpl.allowedFormats;
      
      // Should have 6 extensions: jpg, jpeg, png, webp, heif, heic
      expect(allowedExtensions.length, equals(6));
      
      // Should have 6 MIME types: image/jpeg, image/jpg, image/png, image/webp, image/heif, image/heic
      expect(allowedMimeTypes.length, equals(6));
      
      // Should have 6 formats in repository: jpg, jpeg, png, webp, heif, heic
      expect(allowedFormats.length, equals(6));
    });
  });
}
